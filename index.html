<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="theme-toggle">
        <button id="settingsToggle" class="theme-btn" title="设置">
            <span class="settings-icon">⚙️</span>
        </button>
        <button id="themeToggle" class="theme-btn" title="切换主题">
            <span class="theme-icon">🌙</span>
        </button>
    </div>
    
    <div class="main-container">
        <div class="clock-section">
            <div class="analog-clock">
                <div class="clock-face">
                    <div class="hand hour-hand"></div>
                    <div class="hand minute-hand"></div>
                    <div class="hand second-hand"></div>
                    <div class="center-dot"></div>
                </div>
            </div>
            <div class="digital-time" id="digitalTime"></div>
            
            <div class="weather-section">
                <div class="weather-controls">
                    <div class="weather-select-row">
                        <select id="countrySelect">
                            <option value="china">中国</option>
                            <option value="usa">美国</option>
                            <option value="japan">日本</option>
                            <option value="uk">英国</option>
                            <option value="france">法国</option>
                            <option value="germany">德国</option>
                            <option value="australia">澳大利亚</option>
                            <option value="canada">加拿大</option>
                        </select>
                        <select id="regionSelect">
                            <option value="">选择地区</option>
                        </select>
                    </div>
                    <div class="weather-refresh-row">
                        <button id="refreshWeather">刷新天气</button>
                    </div>
                </div>
                <div class="weather-display" id="weatherDisplay">
                    <div class="weather-loading">加载中...</div>
                </div>
            </div>
        </div>

        <div class="container">
            <h1>高级待办事项列表</h1>
            
            <!-- 文件夹管理 -->
            <div class="folder-section">
                <select id="folderSelect">
                    <option value="default">默认文件夹</option>
                </select>
                <button id="addFolderBtn">新建文件夹</button>
            </div>
            
            <!-- 增强的输入区域 -->
            <div class="enhanced-input-section">
                <input type="text" id="todoInput" placeholder="添加新的待办事项..." />
                <select id="prioritySelect">
                    <option value="critical">极高优先级</option>
                    <option value="high">高优先级</option>
                    <option value="medium" selected>中优先级</option>
                    <option value="low">低优先级</option>
                    <option value="lowest">最低优先级</option>
                </select>
                <div class="category-input-group">
                    <select id="categorySelect">
                        <option value="work">工作</option>
                        <option value="personal">个人</option>
                        <option value="study">学习</option>
                        <option value="health">健康</option>
                        <option value="custom">自定义...</option>
                    </select>
                    <input type="text" id="customCategoryInput" placeholder="输入自定义分类" style="display: none;">
                </div>
                <input type="datetime-local" id="reminderInput" />
                <button id="addBtn">添加</button>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-input-group">
                    <input type="text" id="searchInput" placeholder="搜索待办事项..." />
                    <button id="clearSearchBtn" class="clear-search-btn" title="清空搜索">✕</button>
                </div>
            </div>

            <!-- 过滤和分类区域 -->
            <div class="filter-section">
                <div class="status-filters">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="active">进行中</button>
                    <button class="filter-btn" data-filter="completed">已完成</button>
                </div>
                <div class="category-filters">
                    <button class="category-btn active" data-category="all">全部分类</button>
                    <button class="category-btn" data-category="work">工作</button>
                    <button class="category-btn" data-category="personal">个人</button>
                    <button class="category-btn" data-category="study">学习</button>
                    <button class="category-btn" data-category="health">健康</button>
                    <!-- 自定义分类按钮将动态添加 -->
                </div>
                <div class="priority-filters">
                    <button class="priority-btn active" data-priority="all">全部优先级</button>
                    <button class="priority-btn" data-priority="critical">极高</button>
                    <button class="priority-btn" data-priority="high">高</button>
                    <button class="priority-btn" data-priority="medium">中</button>
                    <button class="priority-btn" data-priority="low">低</button>
                    <button class="priority-btn" data-priority="lowest">最低</button>
                </div>
            </div>
            
            <!-- 提醒列表 -->
            <div class="reminders-section">
                <h3>即将提醒</h3>
                <div id="remindersList"></div>
            </div>
            
            <ul id="todoList" class="todo-list"></ul>
            
            <div class="stats">
                <span id="totalCount">总计: 0</span>
                <span id="activeCount">进行中: 0</span>
                <span id="completedCount">已完成: 0</span>
                <span id="overdueCount">过期: 0</span>
            </div>
        </div>
    </div>
    
    <!-- 设置页面 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ 设置</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>天气API设置</h3>
                    <div class="setting-item">
                        <label for="weatherApiSelect">选择天气API：</label>
                        <select id="weatherApiSelect">
                            <option value="openweathermap">OpenWeatherMap</option>
                            <option value="weatherapi">WeatherAPI</option>
                            <option value="weathermap">WeatherMap</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="apiKeyInput">API密钥：</label>
                        <input type="password" id="apiKeyInput" placeholder="请输入您的API密钥">
                        <div class="setting-help">
                            <p id="apiHelpText">
                                获取免费API密钥的步骤：<br>
                                1. 访问 <a href="https://openweathermap.org/api" target="_blank">OpenWeatherMap API</a><br>
                                2. 注册免费账户<br>
                                3. 在API密钥页面获取您的密钥<br>
                                4. 将密钥粘贴到上方输入框中<br>
                                5. 点击保存按钮
                            </p>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label for="apiStatus">API状态：</label>
                        <div id="apiStatus" class="api-status">
                            <span class="status-indicator"></span>
                            <span class="status-text">未配置</span>
                        </div>
                    </div>
                    <div class="setting-actions">
                        <button id="testApiBtn">测试连接</button>
                        <button id="saveSettingsBtn">保存设置</button>
                        <button id="resetSettingsBtn">重置为默认</button>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>其他设置</h3>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="notificationsEnabled">
                            启用浏览器通知
                        </label>
                        <div class="setting-help">
                            <p>允许浏览器在待办事项到期时显示通知</p>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label for="refreshInterval">天气刷新间隔（分钟）：</label>
                        <select id="refreshInterval">
                            <option value="5">5分钟</option>
                            <option value="10" selected>10分钟</option>
                            <option value="30">30分钟</option>
                            <option value="60">60分钟</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script type="module" src="src/js/main.js"></script>
</body>
</html>
