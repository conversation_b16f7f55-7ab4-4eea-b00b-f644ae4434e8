/**
 * 应用入口文件
 * @fileoverview 应用的主入口，负责初始化和启动应用
 */

import { TodoApp } from './TodoApp.js';
import { UIHelper } from './utils/helpers.js';

/**
 * 应用入口类
 */
class AppBootstrap {
    constructor() {
        this.app = null;
        this.isLoading = true;
        this.loadStartTime = Date.now();
    }

    /**
     * 启动应用
     */
    async start() {
        try {
            // 显示加载状态
            this.showLoadingScreen();
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 等待DOM加载完成
            await this.waitForDOM();
            
            // 初始化应用
            this.app = new TodoApp();
            
            // 隐藏加载状态
            this.hideLoadingScreen();
            
            // 显示启动成功消息
            const loadTime = Date.now() - this.loadStartTime;
            console.log(`应用启动成功，耗时: ${loadTime}ms`);
            
            // 注册全局错误处理
            this.registerErrorHandlers();
            
            // 注册服务工作者（如果支持）
            this.registerServiceWorker();
            
        } catch (error) {
            console.error('应用启动失败:', error);
            this.showErrorScreen(error);
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'localStorage',
            'fetch',
            'Promise',
            'CustomEvent',
            'querySelector'
        ];

        const unsupportedFeatures = requiredFeatures.filter(feature => {
            switch (feature) {
                case 'localStorage':
                    return !window.localStorage;
                case 'fetch':
                    return !window.fetch;
                case 'Promise':
                    return !window.Promise;
                case 'CustomEvent':
                    return !window.CustomEvent;
                case 'querySelector':
                    return !document.querySelector;
                default:
                    return false;
            }
        });

        if (unsupportedFeatures.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${unsupportedFeatures.join(', ')}`);
        }

        // 检查ES6模块支持
        if (!('noModule' in HTMLScriptElement.prototype)) {
            console.warn('浏览器可能不完全支持ES6模块');
        }
    }

    /**
     * 等待DOM加载完成
     * @returns {Promise} Promise对象
     */
    waitForDOM() {
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    /**
     * 显示加载屏幕
     */
    showLoadingScreen() {
        const loadingScreen = document.createElement('div');
        loadingScreen.id = 'app-loading';
        loadingScreen.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载应用...</div>
                <div class="loading-progress">
                    <div class="loading-bar"></div>
                </div>
            </div>
        `;
        
        // 添加加载样式
        const style = document.createElement('style');
        style.textContent = `
            #app-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 99999;
                font-family: Arial, sans-serif;
            }
            
            .loading-container {
                text-align: center;
                color: white;
            }
            
            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 4px solid rgba(255, 255, 255, 0.3);
                border-top: 4px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }
            
            .loading-text {
                font-size: 18px;
                margin-bottom: 20px;
                opacity: 0.9;
            }
            
            .loading-progress {
                width: 200px;
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;
                overflow: hidden;
                margin: 0 auto;
            }
            
            .loading-bar {
                width: 0%;
                height: 100%;
                background: white;
                border-radius: 2px;
                animation: progress 2s ease-in-out infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            @keyframes progress {
                0% { width: 0%; }
                50% { width: 70%; }
                100% { width: 100%; }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(loadingScreen);
    }

    /**
     * 隐藏加载屏幕
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('app-loading');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            
            setTimeout(() => {
                if (loadingScreen.parentNode) {
                    loadingScreen.parentNode.removeChild(loadingScreen);
                }
                this.isLoading = false;
            }, 500);
        }
    }

    /**
     * 显示错误屏幕
     * @param {Error} error - 错误对象
     */
    showErrorScreen(error) {
        const errorScreen = document.createElement('div');
        errorScreen.id = 'app-error';
        errorScreen.innerHTML = `
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-title">应用启动失败</div>
                <div class="error-message">${error.message}</div>
                <div class="error-actions">
                    <button onclick="location.reload()" class="retry-btn">重试</button>
                    <button onclick="this.showDetails()" class="details-btn">详细信息</button>
                </div>
                <div class="error-details" style="display: none;">
                    <pre>${error.stack || '无详细信息'}</pre>
                </div>
            </div>
        `;
        
        // 添加错误样式
        const style = document.createElement('style');
        style.textContent = `
            #app-error {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 99999;
                font-family: Arial, sans-serif;
                color: white;
            }
            
            .error-container {
                text-align: center;
                max-width: 500px;
                padding: 40px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }
            
            .error-icon {
                font-size: 64px;
                margin-bottom: 20px;
            }
            
            .error-title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 15px;
            }
            
            .error-message {
                font-size: 16px;
                margin-bottom: 30px;
                opacity: 0.9;
            }
            
            .error-actions {
                margin-bottom: 20px;
            }
            
            .retry-btn, .details-btn {
                background: white;
                color: #ff6b6b;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin: 0 10px;
                transition: transform 0.2s;
            }
            
            .retry-btn:hover, .details-btn:hover {
                transform: translateY(-2px);
            }
            
            .error-details {
                text-align: left;
                background: rgba(0, 0, 0, 0.3);
                padding: 15px;
                border-radius: 6px;
                font-size: 12px;
                max-height: 200px;
                overflow-y: auto;
            }
        `;
        
        document.head.appendChild(style);
        
        // 隐藏加载屏幕
        this.hideLoadingScreen();
        
        document.body.appendChild(errorScreen);
    }

    /**
     * 注册全局错误处理
     */
    registerErrorHandlers() {
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            UIHelper.showMessage('发生了一个错误，请检查控制台', 'error');
        });

        // 捕获全局JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            UIHelper.showMessage('发生了一个错误，请检查控制台', 'error');
        });
    }

    /**
     * 注册服务工作者
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                // 这里可以注册服务工作者以支持离线功能
                // const registration = await navigator.serviceWorker.register('/sw.js');
                // console.log('Service Worker 注册成功:', registration);
            } catch (error) {
                console.log('Service Worker 注册失败:', error);
            }
        }
    }

    /**
     * 获取应用实例
     * @returns {TodoApp|null} 应用实例
     */
    getApp() {
        return this.app;
    }

    /**
     * 重启应用
     */
    restart() {
        if (this.app) {
            this.app.restart();
        } else {
            location.reload();
        }
    }
}

// 创建应用启动器实例
const bootstrap = new AppBootstrap();

// 启动应用
bootstrap.start();

// 将应用实例暴露到全局（用于调试）
if (typeof window !== 'undefined') {
    window.todoApp = bootstrap;
}

// 导出启动器（用于模块化环境）
export default bootstrap;
