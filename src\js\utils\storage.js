/**
 * 本地存储工具类
 * @fileoverview 提供localStorage的封装和数据持久化功能
 */

import { STORAGE_KEYS, DEFAULT_SETTINGS } from '../constants/config.js';

/**
 * 存储工具类
 */
export class Storage {
    /**
     * 保存数据到localStorage
     * @param {string} key - 存储键名
     * @param {any} data - 要保存的数据
     * @returns {boolean} 是否保存成功
     */
    static save(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`保存数据失败 (${key}):`, error);
            return false;
        }
    }

    /**
     * 从localStorage加载数据
     * @param {string} key - 存储键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 加载的数据或默认值
     */
    static load(key, defaultValue = null) {
        try {
            const saved = localStorage.getItem(key);
            return saved ? JSON.parse(saved) : defaultValue;
        } catch (error) {
            console.error(`加载数据失败 (${key}):`, error);
            return defaultValue;
        }
    }

    /**
     * 删除localStorage中的数据
     * @param {string} key - 存储键名
     * @returns {boolean} 是否删除成功
     */
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error(`删除数据失败 (${key}):`, error);
            return false;
        }
    }

    /**
     * 清空所有应用数据
     * @returns {boolean} 是否清空成功
     */
    static clear() {
        try {
            Object.values(STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 检查localStorage是否可用
     * @returns {boolean} 是否可用
     */
    static isAvailable() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用情况信息
     */
    static getUsage() {
        if (!this.isAvailable()) {
            return { used: 0, total: 0, available: 0 };
        }

        let used = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                used += localStorage[key].length + key.length;
            }
        }

        // 大多数浏览器的localStorage限制为5MB
        const total = 5 * 1024 * 1024;
        const available = total - used;

        return {
            used: used,
            total: total,
            available: available,
            usedPercent: (used / total * 100).toFixed(2)
        };
    }
}

/**
 * 待办事项存储管理
 */
export class TodoStorage {
    /**
     * 保存待办事项列表
     * @param {Array} todos - 待办事项数组
     * @returns {boolean} 是否保存成功
     */
    static save(todos) {
        return Storage.save(STORAGE_KEYS.todos, todos);
    }

    /**
     * 加载待办事项列表
     * @returns {Array} 待办事项数组
     */
    static load() {
        return Storage.load(STORAGE_KEYS.todos, []);
    }

    /**
     * 清空待办事项
     * @returns {boolean} 是否清空成功
     */
    static clear() {
        return Storage.remove(STORAGE_KEYS.todos);
    }
}

/**
 * 文件夹存储管理
 */
export class FolderStorage {
    /**
     * 保存文件夹列表
     * @param {Array} folders - 文件夹数组
     * @returns {boolean} 是否保存成功
     */
    static save(folders) {
        return Storage.save(STORAGE_KEYS.folders, folders);
    }

    /**
     * 加载文件夹列表
     * @returns {Array} 文件夹数组
     */
    static load() {
        const defaultFolders = [
            { id: 'default', name: '默认文件夹' }
        ];
        return Storage.load(STORAGE_KEYS.folders, defaultFolders);
    }

    /**
     * 清空文件夹
     * @returns {boolean} 是否清空成功
     */
    static clear() {
        return Storage.remove(STORAGE_KEYS.folders);
    }
}

/**
 * 天气缓存存储管理
 */
export class WeatherStorage {
    /**
     * 保存天气缓存
     * @param {Object} cache - 天气缓存对象
     * @returns {boolean} 是否保存成功
     */
    static save(cache) {
        return Storage.save(STORAGE_KEYS.weatherCache, cache);
    }

    /**
     * 加载天气缓存
     * @returns {Object} 天气缓存对象
     */
    static load() {
        return Storage.load(STORAGE_KEYS.weatherCache, {});
    }

    /**
     * 清空天气缓存
     * @returns {boolean} 是否清空成功
     */
    static clear() {
        return Storage.remove(STORAGE_KEYS.weatherCache);
    }

    /**
     * 获取特定位置的天气缓存
     * @param {string} locationKey - 位置键名
     * @returns {Object|null} 天气数据或null
     */
    static getLocationCache(locationKey) {
        const cache = this.load();
        const locationCache = cache[locationKey];
        
        if (!locationCache) {
            return null;
        }

        // 检查缓存是否过期（10分钟）
        const now = Date.now();
        if (now - locationCache.timestamp > 600000) {
            return null;
        }

        return locationCache.data;
    }

    /**
     * 设置特定位置的天气缓存
     * @param {string} locationKey - 位置键名
     * @param {Object} data - 天气数据
     * @returns {boolean} 是否保存成功
     */
    static setLocationCache(locationKey, data) {
        const cache = this.load();
        cache[locationKey] = {
            data: data,
            timestamp: Date.now()
        };
        return this.save(cache);
    }
}

/**
 * 设置存储管理
 */
export class SettingsStorage {
    /**
     * 保存设置
     * @param {string} key - 设置键名
     * @param {any} value - 设置值
     * @returns {boolean} 是否保存成功
     */
    static save(key, value) {
        return Storage.save(key, value);
    }

    /**
     * 加载设置
     * @param {string} key - 设置键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 设置值
     */
    static load(key, defaultValue = null) {
        return Storage.load(key, defaultValue);
    }

    /**
     * 加载所有设置
     * @returns {Object} 所有设置对象
     */
    static loadAll() {
        return {
            theme: this.load(STORAGE_KEYS.theme, DEFAULT_SETTINGS.theme),
            weatherApiKey: this.load(STORAGE_KEYS.weatherApiKey, ''),
            weatherApiType: this.load(STORAGE_KEYS.weatherApiType, DEFAULT_SETTINGS.weatherApiType),
            notificationsEnabled: this.load(STORAGE_KEYS.notificationsEnabled, DEFAULT_SETTINGS.notificationsEnabled),
            weatherRefreshInterval: this.load(STORAGE_KEYS.weatherRefreshInterval, DEFAULT_SETTINGS.refreshInterval),
            apiTestSuccess: this.load(STORAGE_KEYS.apiTestSuccess, false),
            apiTestTime: this.load(STORAGE_KEYS.apiTestTime, null)
        };
    }

    /**
     * 保存所有设置
     * @param {Object} settings - 设置对象
     * @returns {boolean} 是否保存成功
     */
    static saveAll(settings) {
        let success = true;
        Object.entries(settings).forEach(([key, value]) => {
            if (STORAGE_KEYS[key]) {
                success = success && this.save(STORAGE_KEYS[key], value);
            }
        });
        return success;
    }

    /**
     * 重置所有设置为默认值
     * @returns {boolean} 是否重置成功
     */
    static reset() {
        return this.saveAll(DEFAULT_SETTINGS);
    }
}
