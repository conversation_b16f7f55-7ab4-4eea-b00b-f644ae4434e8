/**
 * 管理员管理模块
 * @fileoverview 负责管理员后台功能的核心管理
 */

import { SecurityManager } from './SecurityManager.js';
import { DataAnalytics } from './DataAnalytics.js';
import { UserTracker } from './UserTracker.js';
import { Storage } from '../utils/storage.js';
import { DOMHelper, UIHelper, DateHelper } from '../utils/helpers.js';
import { STORAGE_KEYS } from '../constants/config.js';

/**
 * 管理员管理器
 */
export class AdminManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     */
    constructor(elements = {}) {
        this.elements = elements;
        this.securityManager = new SecurityManager();
        this.dataAnalytics = new DataAnalytics();
        this.userTracker = new UserTracker();
        this.isInitialized = false;
        this.refreshInterval = null;
        
        this.init();
    }

    /**
     * 初始化管理员管理器
     */
    async init() {
        // 检查管理员权限
        if (!this.securityManager.hasAdminAccess()) {
            this.showLoginForm();
            return;
        }
        
        await this.initDashboard();
        this.bindEvents();
        this.startAutoRefresh();
        this.isInitialized = true;
    }

    /**
     * 显示登录表单
     */
    showLoginForm() {
        console.log('AdminManager: 显示登录表单');

        const loginContainer = DOMHelper.getElement('#loginContainer');
        const dashboardContainer = DOMHelper.getElement('#dashboardContainer');

        console.log('AdminManager: 容器元素检查:', {
            loginContainer: !!loginContainer,
            dashboardContainer: !!dashboardContainer
        });

        if (loginContainer) {
            loginContainer.style.display = 'block';
            loginContainer.classList.remove('hidden');
            console.log('AdminManager: 登录容器已显示');
        } else {
            console.error('AdminManager: 未找到登录容器 #loginContainer');
        }

        if (dashboardContainer) {
            dashboardContainer.style.display = 'none';
            console.log('AdminManager: 仪表板容器已隐藏');
        }

        // 延迟绑定登录事件，确保DOM元素完全可用
        setTimeout(() => {
            this.bindLoginEvents();
        }, 100);
    }

    /**
     * 绑定登录事件
     */
    bindLoginEvents() {
        console.log('AdminManager: 开始绑定登录事件...');

        const loginBtn = DOMHelper.getElement('#loginBtn');
        const passwordInput = DOMHelper.getElement('#adminPassword');

        console.log('AdminManager: DOM元素检查结果:', {
            loginBtn: !!loginBtn,
            passwordInput: !!passwordInput,
            loginBtnId: loginBtn ? loginBtn.id : 'null',
            passwordInputId: passwordInput ? passwordInput.id : 'null'
        });

        if (loginBtn) {
            console.log('AdminManager: 绑定登录按钮点击事件');
            // 移除之前的事件监听器，避免重复绑定
            loginBtn.removeEventListener('click', this.handleLoginClick);
            // 添加新的事件监听器
            loginBtn.addEventListener('click', this.handleLoginClick.bind(this));

            // 验证事件绑定是否成功
            console.log('AdminManager: 登录按钮事件绑定完成');
        } else {
            console.error('AdminManager: 未找到登录按钮元素 #loginBtn');
        }

        if (passwordInput) {
            console.log('AdminManager: 绑定密码输入框回车事件');
            // 移除之前的事件监听器，避免重复绑定
            passwordInput.removeEventListener('keypress', this.handlePasswordKeyPress);
            // 添加新的事件监听器
            passwordInput.addEventListener('keypress', this.handlePasswordKeyPress.bind(this));

            console.log('AdminManager: 密码输入框事件绑定完成');
        } else {
            console.error('AdminManager: 未找到密码输入框元素 #adminPassword');
        }
    }

    /**
     * 处理登录按钮点击事件
     */
    handleLoginClick(event) {
        console.log('AdminManager: 登录按钮被点击', event);
        event.preventDefault(); // 防止表单默认提交
        this.handleLogin();
    }

    /**
     * 处理密码输入框回车事件
     * @param {Event} event - 键盘事件
     */
    handlePasswordKeyPress(event) {
        if (event.key === 'Enter') {
            this.handleLogin();
        }
    }

    /**
     * 处理管理员登录
     */
    async handleLogin() {
        console.log('开始处理管理员登录...');
        
        const passwordInput = DOMHelper.getElement('#adminPassword');
        const loginBtn = DOMHelper.getElement('#loginBtn');
        const errorMsg = DOMHelper.getElement('#loginError');
        
        console.log('获取到的元素:', {
            passwordInput: !!passwordInput,
            loginBtn: !!loginBtn,
            errorMsg: !!errorMsg
        });
        
        if (!passwordInput) {
            console.error('未找到密码输入框');
            return;
        }
        
        const password = passwordInput.value.trim();
        console.log('输入的密码长度:', password.length);
        
        if (!password) {
            this.showLoginError('请输入管理员密码');
            return;
        }
        
        // 显示加载状态
        if (loginBtn) {
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
        }
        
        try {
            console.log('开始验证密码...');
            const success = await this.securityManager.adminLogin(password);
            console.log('密码验证结果:', success);
            
            if (success) {
                console.log('登录成功，初始化仪表板...');
                passwordInput.value = '';
                if (errorMsg) errorMsg.style.display = 'none';
                await this.initDashboard();
                this.showDashboard();
                UIHelper.showMessage('管理员登录成功', 'success');
            } else {
                console.log('密码验证失败');
                this.showLoginError('密码错误，请重试');
            }
        } catch (error) {
            console.error('登录失败:', error);
            this.showLoginError('登录失败，请稍后重试');
        } finally {
            if (loginBtn) {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }
    }

    /**
     * 显示登录错误
     * @param {string} message - 错误消息
     */
    showLoginError(message) {
        const errorMsg = DOMHelper.getElement('#loginError');
        if (errorMsg) {
            errorMsg.textContent = message;
            errorMsg.style.display = 'block';
        }
    }

    /**
     * 显示仪表板
     */
    showDashboard() {
        const loginContainer = DOMHelper.getElement('#loginContainer');
        const dashboardContainer = DOMHelper.getElement('#dashboardContainer');
        
        if (loginContainer) {
            loginContainer.style.display = 'none';
            loginContainer.classList.add('hidden');
        }
        if (dashboardContainer) {
            dashboardContainer.style.display = 'block';
        }
        
        // 重新绑定事件以确保标签页按钮正常工作
        this.bindTabEvents();
    }

    /**
     * 初始化仪表板
     */
    async initDashboard() {
        await this.loadOverviewData();
        await this.loadUserList();
        await this.loadActivityTrend();
        await this.loadEventDistribution();
        this.loadSystemInfo();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登出按钮
        const logoutBtn = DOMHelper.getElement('#logoutBtn');
        if (logoutBtn) {
            DOMHelper.addEventListener(logoutBtn, 'click', () => this.handleLogout());
        }
        
        // 刷新按钮
        const refreshBtn = DOMHelper.getElement('#refreshBtn');
        if (refreshBtn) {
            DOMHelper.addEventListener(refreshBtn, 'click', () => this.refreshDashboard());
        }
        
        // 导出按钮
        const exportJsonBtn = DOMHelper.getElement('#exportJsonBtn');
        const exportCsvBtn = DOMHelper.getElement('#exportCsvBtn');
        
        if (exportJsonBtn) {
            DOMHelper.addEventListener(exportJsonBtn, 'click', () => this.exportData('json'));
        }
        
        if (exportCsvBtn) {
            DOMHelper.addEventListener(exportCsvBtn, 'click', () => this.exportData('csv'));
        }
        
        // 清理数据按钮
        const cleanupBtn = DOMHelper.getElement('#cleanupBtn');
        if (cleanupBtn) {
            DOMHelper.addEventListener(cleanupBtn, 'click', () => this.cleanupOldData());
        }
        
        // 绑定标签页事件
        this.bindTabEvents();
    }

    /**
     * 绑定标签页事件
     */
    bindTabEvents() {
        const tabBtns = DOMHelper.getElements('.tab-btn');
        tabBtns.forEach(btn => {
            // 移除之前的事件监听器，避免重复绑定
            btn.removeEventListener('click', this.handleTabClick);
            // 添加新的事件监听器
            btn.addEventListener('click', this.handleTabClick.bind(this));
        });
    }

    /**
     * 处理标签页点击事件
     * @param {Event} event - 点击事件
     */
    handleTabClick(event) {
        const tabName = event.target.dataset.tab;
        if (tabName) {
            this.switchTab(tabName);
        }
    }

    /**
     * 加载概览数据
     */
    async loadOverviewData() {
        const overview = this.dataAnalytics.getUserOverview();
        
        // 更新概览卡片
        this.updateElement('#totalUsers', overview.totalUsers);
        this.updateElement('#activeUsers7d', overview.activeUsers7d);
        this.updateElement('#todayActiveUsers', overview.todayActiveUsers);
        this.updateElement('#totalSessions', overview.totalSessions);
        this.updateElement('#totalEvents', overview.totalEvents);
        this.updateElement('#avgEventsPerUser', overview.avgEventsPerUser);
    }

    /**
     * 加载用户列表
     */
    async loadUserList() {
        const users = this.dataAnalytics.getUserList({ limit: 50 });
        const userListContainer = DOMHelper.getElement('#userList');
        
        if (!userListContainer) return;
        
        userListContainer.innerHTML = '';
        
        if (users.length === 0) {
            userListContainer.innerHTML = '<div class="no-data">暂无用户数据</div>';
            return;
        }
        
        users.forEach(user => {
            const userRow = this.createUserRow(user);
            userListContainer.appendChild(userRow);
        });
    }

    /**
     * 创建用户行
     * @param {Object} user - 用户数据
     * @returns {Element} 用户行元素
     */
    createUserRow(user) {
        const row = DOMHelper.createElement('div', { className: 'user-row' });
        
        const lastSeenText = user.daysSinceLastSeen === 0 ? '今天' : `${user.daysSinceLastSeen}天前`;
        const ipAddress = user.ipAddress || '未知';
        
        row.innerHTML = `
            <div class="user-id">${user.userId.substring(0, 16)}...</div>
            <div class="user-ip">${ipAddress}</div>
            <div class="user-events">${user.eventCount}</div>
            <div class="user-sessions">${user.sessionCount}</div>
            <div class="user-last-seen">${lastSeenText}</div>
            <div class="user-actions">
                <button class="btn-small" onclick="adminManager.viewUserDetails('${user.userId}')">详情</button>
            </div>
        `;
        
        return row;
    }

    /**
     * 加载活跃度趋势
     */
    async loadActivityTrend() {
        const trendData = this.dataAnalytics.getActivityTrend('daily', 14);
        this.renderLineChart('#activityChart', trendData);
    }

    /**
     * 加载事件分布
     */
    async loadEventDistribution() {
        const distribution = this.dataAnalytics.getEventTypeDistribution();
        this.renderPieChart('#eventChart', distribution);
    }

    /**
     * 渲染折线图
     * @param {string} selector - 容器选择器
     * @param {Object} data - 图表数据
     */
    renderLineChart(selector, data) {
        const container = DOMHelper.getElement(selector);
        if (!container) return;
        
        const canvas = DOMHelper.createElement('canvas', {
            width: '400',
            height: '200'
        });
        
        container.innerHTML = '';
        container.appendChild(canvas);
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        const padding = 40;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        if (data.data.length === 0) {
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', width / 2, height / 2);
            return;
        }
        
        // 计算数据范围
        const maxValue = Math.max(...data.data.map(d => d.activeUsers));
        const minValue = 0;
        
        // 绘制坐标轴
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, height - padding);
        ctx.lineTo(width - padding, height - padding);
        ctx.stroke();
        
        // 绘制数据线
        if (data.data.length > 1) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            data.data.forEach((point, index) => {
                const x = padding + (index / (data.data.length - 1)) * (width - 2 * padding);
                const y = height - padding - ((point.activeUsers - minValue) / (maxValue - minValue)) * (height - 2 * padding);
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            ctx.fillStyle = '#667eea';
            data.data.forEach((point, index) => {
                const x = padding + (index / (data.data.length - 1)) * (width - 2 * padding);
                const y = height - padding - ((point.activeUsers - minValue) / (maxValue - minValue)) * (height - 2 * padding);
                
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        // 添加标签
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        data.data.forEach((point, index) => {
            if (index % Math.ceil(data.data.length / 5) === 0) {
                const x = padding + (index / (data.data.length - 1)) * (width - 2 * padding);
                ctx.fillText(point.period, x, height - 10);
            }
        });
    }

    /**
     * 渲染饼图
     * @param {string} selector - 容器选择器
     * @param {Object} data - 图表数据
     */
    renderPieChart(selector, data) {
        const container = DOMHelper.getElement(selector);
        if (!container) return;
        
        container.innerHTML = '';
        
        if (data.distribution.length === 0) {
            container.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }
        
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];
        
        data.distribution.slice(0, 8).forEach((item, index) => {
            const row = DOMHelper.createElement('div', { className: 'chart-row' });
            row.innerHTML = `
                <div class="chart-color" style="background-color: ${colors[index % colors.length]}"></div>
                <div class="chart-label">${item.name}</div>
                <div class="chart-value">${item.value} (${item.percentage}%)</div>
            `;
            container.appendChild(row);
        });
    }

    /**
     * 加载系统信息
     */
    loadSystemInfo() {
        const sessionInfo = this.securityManager.getAdminSessionInfo();
        const systemInfo = {
            version: '2.0.0',
            lastUpdate: new Date().toLocaleDateString('zh-CN'),
            sessionExpiry: sessionInfo ? new Date(sessionInfo.expiry).toLocaleString('zh-CN') : '未知'
        };
        
        this.updateElement('#systemVersion', systemInfo.version);
        this.updateElement('#lastUpdate', systemInfo.lastUpdate);
        this.updateElement('#sessionExpiry', systemInfo.sessionExpiry);
    }

    /**
     * 更新元素内容
     * @param {string} selector - 元素选择器
     * @param {any} value - 新值
     */
    updateElement(selector, value) {
        const element = DOMHelper.getElement(selector);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * 处理登出
     */
    handleLogout() {
        if (confirm('确定要退出管理员后台吗？')) {
            this.securityManager.adminLogout();
            this.stopAutoRefresh();
            this.showLoginForm();
            UIHelper.showMessage('已安全退出', 'success');
        }
    }

    /**
     * 刷新仪表板
     */
    async refreshDashboard() {
        const refreshBtn = DOMHelper.getElement('#refreshBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.textContent = '刷新中...';
        }
        
        try {
            await this.initDashboard();
            UIHelper.showMessage('数据已刷新', 'success');
        } catch (error) {
            console.error('刷新失败:', error);
            UIHelper.showMessage('刷新失败', 'error');
        } finally {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.textContent = '刷新数据';
            }
        }
    }

    /**
     * 导出数据
     * @param {string} format - 导出格式
     */
    exportData(format) {
        try {
            const data = this.dataAnalytics.exportData(format, {
                includeAnalytics: true,
                includeSessions: true,
                sanitize: true
            });
            
            const blob = new Blob([data], { 
                type: format === 'csv' ? 'text/csv' : 'application/json' 
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bigbluebook_data_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            UIHelper.showMessage(`数据已导出为 ${format.toUpperCase()} 格式`, 'success');
        } catch (error) {
            console.error('导出失败:', error);
            UIHelper.showMessage('导出失败', 'error');
        }
    }

    /**
     * 清理旧数据
     */
    cleanupOldData() {
        if (confirm('确定要清理30天前的旧数据吗？此操作不可撤销。')) {
            try {
                this.securityManager.cleanupLogs(30);
                // 这里可以添加更多清理逻辑
                UIHelper.showMessage('旧数据清理完成', 'success');
                this.refreshDashboard();
            } catch (error) {
                console.error('清理失败:', error);
                UIHelper.showMessage('清理失败', 'error');
            }
        }
    }

    /**
     * 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        if (!tabName) return;
        
        console.log('切换到标签页:', tabName);
        
        // 更新标签按钮状态
        const tabBtns = DOMHelper.getElements('.tab-btn');
        tabBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            }
        });
        
        // 显示对应的标签页内容
        const tabContents = DOMHelper.getElements('.tab-content');
        let hasActiveTab = false;
        
        tabContents.forEach(content => {
            content.style.display = 'none';
            if (content.id === `${tabName}Tab`) {
                content.style.display = 'block';
                hasActiveTab = true;
            }
        });
        
        if (!hasActiveTab) {
            console.warn(`未找到标签页: ${tabName}Tab`);
        }
    }

    /**
     * 查看用户详情
     * @param {string} userId - 用户ID
     */
    viewUserDetails(userId) {
        // 这里可以实现用户详情模态框
        UIHelper.showMessage(`查看用户 ${userId} 的详情功能待实现`, 'info');
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadOverviewData();
        }, 60000); // 每分钟刷新一次概览数据
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 销毁管理员管理器
     */
    destroy() {
        this.stopAutoRefresh();
        this.isInitialized = false;
    }
}
