/**
 * 用户跟踪模块
 * @fileoverview 负责用户行为跟踪、会话管理和数据收集
 */

import { Storage } from '../utils/storage.js';
import { STORAGE_KEYS, TRACKING_CONFIG, ADMIN_CONFIG } from '../constants/config.js';
import { StringHelper, DateHelper } from '../utils/helpers.js';

/**
 * 用户跟踪器
 */
export class UserTracker {
    /**
     * 构造函数
     */
    constructor() {
        this.userId = null;
        this.sessionId = null;
        this.userFingerprint = null;
        this.ipAddress = null;
        this.isTrackingEnabled = true;
        this.privacySettings = this.loadPrivacySettings();
        
        this.init();
    }

    /**
     * 初始化用户跟踪
     */
    async init() {
        // 检查隐私设置
        if (!this.privacySettings.dataCollection) {
            this.isTrackingEnabled = false;
            return;
        }

        // 生成用户指纹
        this.userFingerprint = await this.generateFingerprint();
        
        // 生成或获取用户ID
        this.userId = this.getOrCreateUserId();
        
        // 创建新会话
        this.sessionId = this.createSession();
        
        // 获取IP地址
        await this.fetchIPAddress();
        
        // 记录应用启动事件
        this.trackEvent(TRACKING_CONFIG.EVENTS.APP_START, {
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            referrer: document.referrer
        });
    }

    /**
     * 生成用户指纹
     * @returns {Promise<string>} 用户指纹
     */
    async generateFingerprint() {
        const components = [];
        
        // 收集指纹组件
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('userAgent')) {
            components.push(navigator.userAgent);
        }
        
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('language')) {
            components.push(navigator.language);
        }
        
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('platform')) {
            components.push(navigator.platform);
        }
        
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('screenResolution')) {
            components.push(`${screen.width}x${screen.height}`);
        }
        
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('timezone')) {
            components.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
        }
        
        if (TRACKING_CONFIG.FINGERPRINT_COMPONENTS.includes('colorDepth')) {
            components.push(screen.colorDepth);
        }
        
        // 生成指纹哈希
        const fingerprintString = components.join('|');
        return await this.hashString(fingerprintString);
    }

    /**
     * 字符串哈希函数
     * @param {string} str - 要哈希的字符串
     * @returns {Promise<string>} 哈希值
     */
    async hashString(str) {
        if (crypto && crypto.subtle) {
            const encoder = new TextEncoder();
            const data = encoder.encode(str);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } else {
            // 降级到简单哈希
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash).toString(16);
        }
    }

    /**
     * 获取或创建用户ID
     * @returns {string} 用户ID
     */
    getOrCreateUserId() {
        const existingUserId = Storage.load('userId');
        if (existingUserId) {
            return existingUserId;
        }
        
        // 基于指纹生成用户ID
        const userId = `user_${this.userFingerprint.substring(0, 16)}`;
        Storage.save('userId', userId);
        return userId;
    }

    /**
     * 创建新会话
     * @returns {string} 会话ID
     */
    createSession() {
        const sessionId = `session_${Date.now()}_${StringHelper.generateId(8)}`;
        const sessionData = {
            id: sessionId,
            userId: this.userId,
            startTime: Date.now(),
            userAgent: navigator.userAgent,
            ipAddress: this.ipAddress,
            fingerprint: this.userFingerprint
        };
        
        // 保存会话数据
        const sessions = Storage.load(STORAGE_KEYS.userSessions, []);
        sessions.push(sessionData);
        
        // 清理过期会话
        this.cleanupExpiredSessions(sessions);
        
        Storage.save(STORAGE_KEYS.userSessions, sessions);
        return sessionId;
    }

    /**
     * 清理过期会话
     * @param {Array} sessions - 会话列表
     */
    cleanupExpiredSessions(sessions) {
        const now = Date.now();
        const validSessions = sessions.filter(session => 
            now - session.startTime < ADMIN_CONFIG.SESSION_TIMEOUT
        );
        
        if (validSessions.length !== sessions.length) {
            Storage.save(STORAGE_KEYS.userSessions, validSessions);
        }
    }

    /**
     * 获取IP地址
     */
    async fetchIPAddress() {
        if (!this.privacySettings.ipCollection) {
            return;
        }

        try {
            const response = await fetch(ADMIN_CONFIG.IP_API_URL);
            const data = await response.json();
            this.ipAddress = data.ip;
        } catch (error) {
            try {
                // 尝试备用API
                const response = await fetch(ADMIN_CONFIG.IP_API_FALLBACK);
                const data = await response.json();
                this.ipAddress = data.origin;
            } catch (fallbackError) {
                console.warn('无法获取IP地址:', fallbackError);
                this.ipAddress = 'unknown';
            }
        }
    }

    /**
     * 跟踪事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    trackEvent(eventType, eventData = {}) {
        if (!this.isTrackingEnabled) {
            return;
        }

        const event = {
            id: StringHelper.generateId(16),
            type: eventType,
            userId: this.userId,
            sessionId: this.sessionId,
            timestamp: Date.now(),
            data: this.privacySettings.anonymizeData ? this.anonymizeData(eventData) : eventData
        };

        // 保存事件到分析数据
        const analytics = Storage.load(STORAGE_KEYS.userAnalytics, []);
        analytics.push(event);
        
        // 限制数据大小，只保留最近的数据
        if (analytics.length > 10000) {
            analytics.splice(0, analytics.length - 10000);
        }
        
        Storage.save(STORAGE_KEYS.userAnalytics, analytics);
    }

    /**
     * 数据匿名化处理
     * @param {Object} data - 原始数据
     * @returns {Object} 匿名化后的数据
     */
    anonymizeData(data) {
        const anonymized = { ...data };
        
        // 移除或哈希敏感信息
        if (anonymized.todoText) {
            anonymized.todoText = `[${anonymized.todoText.length} chars]`;
        }
        
        if (anonymized.userAgent) {
            anonymized.userAgent = anonymized.userAgent.substring(0, 50) + '...';
        }
        
        if (anonymized.ipAddress) {
            // 只保留IP的前三段
            const ipParts = anonymized.ipAddress.split('.');
            if (ipParts.length === 4) {
                anonymized.ipAddress = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.xxx`;
            }
        }
        
        return anonymized;
    }

    /**
     * 加载隐私设置
     * @returns {Object} 隐私设置
     */
    loadPrivacySettings() {
        return Storage.load(STORAGE_KEYS.privacySettings, {
            dataCollection: true,
            ipCollection: true,
            anonymizeData: true,
            retentionDays: 30
        });
    }

    /**
     * 更新隐私设置
     * @param {Object} settings - 新的隐私设置
     */
    updatePrivacySettings(settings) {
        this.privacySettings = { ...this.privacySettings, ...settings };
        Storage.save(STORAGE_KEYS.privacySettings, this.privacySettings);
        
        // 如果禁用了数据收集，停止跟踪
        if (!this.privacySettings.dataCollection) {
            this.isTrackingEnabled = false;
        }
    }

    /**
     * 获取用户统计信息
     * @returns {Object} 用户统计
     */
    getUserStats() {
        const analytics = Storage.load(STORAGE_KEYS.userAnalytics, []);
        const userEvents = analytics.filter(event => event.userId === this.userId);
        
        return {
            userId: this.userId,
            totalEvents: userEvents.length,
            firstSeen: userEvents.length > 0 ? Math.min(...userEvents.map(e => e.timestamp)) : null,
            lastSeen: userEvents.length > 0 ? Math.max(...userEvents.map(e => e.timestamp)) : null,
            sessionCount: new Set(userEvents.map(e => e.sessionId)).size,
            eventTypes: this.getEventTypeCounts(userEvents)
        };
    }

    /**
     * 获取事件类型统计
     * @param {Array} events - 事件列表
     * @returns {Object} 事件类型统计
     */
    getEventTypeCounts(events) {
        const counts = {};
        events.forEach(event => {
            counts[event.type] = (counts[event.type] || 0) + 1;
        });
        return counts;
    }

    /**
     * 清理用户数据
     */
    clearUserData() {
        Storage.remove('userId');
        Storage.remove(STORAGE_KEYS.userSessions);
        Storage.remove(STORAGE_KEYS.userAnalytics);
        this.userId = null;
        this.sessionId = null;
    }

    /**
     * 获取当前用户ID
     * @returns {string} 用户ID
     */
    getCurrentUserId() {
        return this.userId;
    }

    /**
     * 获取当前会话ID
     * @returns {string} 会话ID
     */
    getCurrentSessionId() {
        return this.sessionId;
    }
}
