/**
 * 文件夹管理模块
 * @fileoverview 负责待办事项文件夹的创建、管理和切换
 */

import { FolderStorage } from '../utils/storage.js';
import { DOMHelper, StringHelper, UIHelper } from '../utils/helpers.js';

/**
 * 文件夹管理器
 */
export class FolderManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     * @param {Function} onFolderChange - 文件夹切换回调函数
     */
    constructor(elements, onFolderChange = null) {
        this.elements = elements;
        this.onFolderChange = onFolderChange;
        this.folders = FolderStorage.load();
        this.currentFolder = 'default';
        
        this.init();
    }

    /**
     * 初始化文件夹管理器
     */
    init() {
        this.bindEvents();
        this.updateFolderSelect();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加文件夹按钮事件
        DOMHelper.addEventListener(this.elements.addFolderBtn, 'click', () => {
            this.addFolder();
        });

        // 文件夹选择变化事件
        DOMHelper.addEventListener(this.elements.folderSelect, 'change', (e) => {
            this.setCurrentFolder(e.target.value);
        });

        // 文件夹右键菜单事件
        DOMHelper.addEventListener(this.elements.folderSelect, 'contextmenu', (e) => {
            e.preventDefault();
            this.showFolderContextMenu(e);
        });
    }

    /**
     * 添加新文件夹
     */
    addFolder() {
        const folderName = prompt('请输入文件夹名称：');
        if (folderName && folderName.trim()) {
            const trimmedName = folderName.trim();
            
            // 检查文件夹名称是否已存在
            if (this.folders.some(folder => folder.name === trimmedName)) {
                UIHelper.showMessage('文件夹名称已存在', 'warning');
                return;
            }

            // 验证文件夹名称
            if (!this.isValidFolderName(trimmedName)) {
                UIHelper.showMessage('文件夹名称包含无效字符', 'error');
                return;
            }

            const folderId = this.generateFolderId();
            const newFolder = {
                id: folderId,
                name: trimmedName,
                createdAt: new Date().toISOString(),
                color: this.getRandomColor()
            };

            this.folders.push(newFolder);
            this.saveFolders();
            this.updateFolderSelect();
            
            // 自动切换到新文件夹
            this.setCurrentFolder(folderId);
            
            UIHelper.showMessage(`文件夹"${trimmedName}"创建成功`);
        }
    }

    /**
     * 删除文件夹
     * @param {string} folderId - 文件夹ID
     */
    deleteFolder(folderId) {
        if (folderId === 'default') {
            UIHelper.showMessage('默认文件夹不能删除', 'warning');
            return;
        }

        const folder = this.folders.find(f => f.id === folderId);
        if (!folder) {
            UIHelper.showMessage('文件夹不存在', 'error');
            return;
        }

        if (confirm(`确定要删除文件夹"${folder.name}"吗？\n注意：文件夹中的待办事项将被移动到默认文件夹。`)) {
            // 移除文件夹
            this.folders = this.folders.filter(f => f.id !== folderId);
            this.saveFolders();
            
            // 如果当前选中的是被删除的文件夹，切换到默认文件夹
            if (this.currentFolder === folderId) {
                this.setCurrentFolder('default');
            }
            
            this.updateFolderSelect();
            UIHelper.showMessage(`文件夹"${folder.name}"已删除`);
            
            // 通知需要移动待办事项
            this.notifyFolderDeleted(folderId);
        }
    }

    /**
     * 重命名文件夹
     * @param {string} folderId - 文件夹ID
     */
    renameFolder(folderId) {
        if (folderId === 'default') {
            UIHelper.showMessage('默认文件夹不能重命名', 'warning');
            return;
        }

        const folder = this.folders.find(f => f.id === folderId);
        if (!folder) {
            UIHelper.showMessage('文件夹不存在', 'error');
            return;
        }

        const newName = prompt('请输入新的文件夹名称：', folder.name);
        if (newName && newName.trim() && newName.trim() !== folder.name) {
            const trimmedName = newName.trim();
            
            // 检查文件夹名称是否已存在
            if (this.folders.some(f => f.name === trimmedName && f.id !== folderId)) {
                UIHelper.showMessage('文件夹名称已存在', 'warning');
                return;
            }

            // 验证文件夹名称
            if (!this.isValidFolderName(trimmedName)) {
                UIHelper.showMessage('文件夹名称包含无效字符', 'error');
                return;
            }

            const oldName = folder.name;
            folder.name = trimmedName;
            folder.updatedAt = new Date().toISOString();
            
            this.saveFolders();
            this.updateFolderSelect();
            
            UIHelper.showMessage(`文件夹"${oldName}"已重命名为"${trimmedName}"`);
        }
    }

    /**
     * 设置当前文件夹
     * @param {string} folderId - 文件夹ID
     */
    setCurrentFolder(folderId) {
        const folder = this.folders.find(f => f.id === folderId);
        if (!folder && folderId !== 'default') {
            UIHelper.showMessage('文件夹不存在', 'error');
            return;
        }

        this.currentFolder = folderId;
        this.elements.folderSelect.value = folderId;
        
        // 通知文件夹变化
        if (typeof this.onFolderChange === 'function') {
            this.onFolderChange(folderId);
        }
        
        // 分发文件夹变化事件
        this.dispatchFolderEvent('folderChanged', {
            folderId,
            folderName: folder ? folder.name : '默认文件夹'
        });
    }

    /**
     * 更新文件夹选择器
     */
    updateFolderSelect() {
        // 清空现有选项
        this.elements.folderSelect.innerHTML = '';
        
        // 添加文件夹选项
        this.folders.forEach(folder => {
            const option = DOMHelper.createElement('option', {
                value: folder.id
            }, folder.name);
            
            // 为非默认文件夹添加颜色标识
            if (folder.color && folder.id !== 'default') {
                option.style.color = folder.color;
            }
            
            this.elements.folderSelect.appendChild(option);
        });
        
        // 设置当前选中的文件夹
        this.elements.folderSelect.value = this.currentFolder;
    }

    /**
     * 显示文件夹右键菜单
     * @param {Event} event - 右键事件
     */
    showFolderContextMenu(event) {
        const selectedFolderId = this.elements.folderSelect.value;
        if (selectedFolderId === 'default') {
            return; // 默认文件夹不显示右键菜单
        }

        // 创建右键菜单
        const menu = this.createContextMenu([
            {
                label: '重命名',
                action: () => this.renameFolder(selectedFolderId)
            },
            {
                label: '删除',
                action: () => this.deleteFolder(selectedFolderId),
                className: 'danger'
            }
        ]);

        // 显示菜单
        this.showContextMenu(menu, event.clientX, event.clientY);
    }

    /**
     * 创建右键菜单
     * @param {Array} items - 菜单项
     * @returns {Element} 菜单元素
     */
    createContextMenu(items) {
        const menu = DOMHelper.createElement('div', {
            className: 'context-menu',
            style: `
                position: fixed;
                background: var(--bg-secondary);
                border: 1px solid var(--border-color);
                border-radius: 6px;
                box-shadow: 0 4px 12px var(--shadow-color);
                z-index: 10000;
                min-width: 120px;
                padding: 4px 0;
            `
        });

        items.forEach(item => {
            const menuItem = DOMHelper.createElement('div', {
                className: `context-menu-item ${item.className || ''}`,
                style: `
                    padding: 8px 16px;
                    cursor: pointer;
                    font-size: 14px;
                    color: var(--text-primary);
                    transition: background-color 0.2s;
                `
            }, item.label);

            // 添加悬停效果
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.backgroundColor = 'var(--hover-bg)';
            });

            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.backgroundColor = 'transparent';
            });

            // 添加点击事件
            DOMHelper.addEventListener(menuItem, 'click', () => {
                item.action();
                this.hideContextMenu();
            });

            menu.appendChild(menuItem);
        });

        return menu;
    }

    /**
     * 显示右键菜单
     * @param {Element} menu - 菜单元素
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    showContextMenu(menu, x, y) {
        // 移除现有菜单
        this.hideContextMenu();

        // 设置菜单位置
        menu.style.left = x + 'px';
        menu.style.top = y + 'px';

        document.body.appendChild(menu);

        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                this.hideContextMenu();
                document.removeEventListener('click', closeMenu);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 0);
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        const existingMenu = document.querySelector('.context-menu');
        if (existingMenu && existingMenu.parentNode) {
            existingMenu.parentNode.removeChild(existingMenu);
        }
    }

    /**
     * 验证文件夹名称
     * @param {string} name - 文件夹名称
     * @returns {boolean} 是否有效
     */
    isValidFolderName(name) {
        // 检查长度
        if (name.length === 0 || name.length > 50) {
            return false;
        }

        // 检查无效字符
        const invalidChars = /[<>:"/\\|?*]/;
        return !invalidChars.test(name);
    }

    /**
     * 生成文件夹ID
     * @returns {string} 文件夹ID
     */
    generateFolderId() {
        return 'folder_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取随机颜色
     * @returns {string} 颜色值
     */
    getRandomColor() {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 通知文件夹删除
     * @param {string} folderId - 被删除的文件夹ID
     */
    notifyFolderDeleted(folderId) {
        this.dispatchFolderEvent('folderDeleted', { folderId });
    }

    /**
     * 分发文件夹事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchFolderEvent(eventType, detail) {
        const event = new CustomEvent(eventType, {
            detail,
            bubbles: true,
            cancelable: true
        });
        document.dispatchEvent(event);
    }

    /**
     * 保存文件夹到本地存储
     */
    saveFolders() {
        FolderStorage.save(this.folders);
    }

    /**
     * 获取所有文件夹
     * @returns {Array} 文件夹列表
     */
    getAllFolders() {
        return this.folders;
    }

    /**
     * 获取当前文件夹
     * @returns {string} 当前文件夹ID
     */
    getCurrentFolder() {
        return this.currentFolder;
    }

    /**
     * 获取文件夹信息
     * @param {string} folderId - 文件夹ID
     * @returns {Object|null} 文件夹信息
     */
    getFolderInfo(folderId) {
        return this.folders.find(f => f.id === folderId) || null;
    }

    /**
     * 获取文件夹统计信息
     * @returns {Object} 统计信息
     */
    getFolderStats() {
        return {
            totalFolders: this.folders.length,
            currentFolder: this.currentFolder,
            defaultFolder: this.folders.find(f => f.id === 'default'),
            customFolders: this.folders.filter(f => f.id !== 'default').length
        };
    }

    /**
     * 导出文件夹数据
     * @returns {Object} 文件夹数据
     */
    exportFolders() {
        return {
            folders: this.folders,
            currentFolder: this.currentFolder,
            timestamp: Date.now()
        };
    }

    /**
     * 导入文件夹数据
     * @param {Object} data - 文件夹数据
     * @returns {boolean} 是否导入成功
     */
    importFolders(data) {
        try {
            if (data.folders && Array.isArray(data.folders)) {
                this.folders = data.folders;
                this.saveFolders();
                this.updateFolderSelect();
                
                if (data.currentFolder) {
                    this.setCurrentFolder(data.currentFolder);
                }
                
                UIHelper.showMessage('文件夹数据导入成功');
                return true;
            }
            throw new Error('文件夹数据格式不正确');
        } catch (error) {
            UIHelper.showMessage('文件夹数据导入失败：' + error.message, 'error');
            return false;
        }
    }

    /**
     * 销毁文件夹管理器
     */
    destroy() {
        this.hideContextMenu();
        this.onFolderChange = null;
        this.folders = null;
    }
}
