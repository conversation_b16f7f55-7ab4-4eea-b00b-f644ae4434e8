/**
 * 待办事项管理模块
 * @fileoverview 负责待办事项的增删改查和过滤功能
 */

import { TodoStorage } from '../utils/storage.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>per, DataHelper, UIHelper } from '../utils/helpers.js';
import { TODO_CONFIG, TRACKING_CONFIG } from '../constants/config.js';

/**
 * 待办事项管理器
 */
export class TodoManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     */
    constructor(elements) {
        this.elements = elements;
        this.todos = TodoStorage.load();
        this.currentFilter = 'all';
        this.currentCategory = 'all';
        this.currentPriority = 'all';
        this.currentFolder = 'default';
        this.searchQuery = '';
        this.customCategories = this.loadCustomCategories();
        this.reminders = [];
        this.userTracker = null; // 将在主应用中设置

        this.bindEvents();
        this.initCustomCategories();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加待办事项
        DOMHelper.addEventListener(this.elements.addBtn, 'click', () => this.addTodo());
        DOMHelper.addEventListener(this.elements.todoInput, 'keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTodo();
            }
        });

        // 搜索功能事件
        const searchInput = DOMHelper.getElement('#searchInput');
        const clearSearchBtn = DOMHelper.getElement('#clearSearchBtn');

        if (searchInput) {
            DOMHelper.addEventListener(searchInput, 'input', UIHelper.debounce((e) => {
                this.setSearchQuery(e.target.value);
            }, 300));

            DOMHelper.addEventListener(searchInput, 'keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                }
            });
        }

        if (clearSearchBtn) {
            DOMHelper.addEventListener(clearSearchBtn, 'click', () => {
                this.clearSearch();
            });
        }

        // 自定义分类事件
        const categorySelect = DOMHelper.getElement('#categorySelect');
        const customCategoryInput = DOMHelper.getElement('#customCategoryInput');

        if (categorySelect) {
            DOMHelper.addEventListener(categorySelect, 'change', (e) => {
                this.handleCategorySelectChange(e.target.value);
            });
        }

        if (customCategoryInput) {
            DOMHelper.addEventListener(customCategoryInput, 'keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleCustomCategoryInput();
                }
            });

            DOMHelper.addEventListener(customCategoryInput, 'blur', () => {
                this.handleCustomCategoryInput();
            });
        }

        // 过滤按钮事件
        this.elements.filterBtns.forEach(btn => {
            DOMHelper.addEventListener(btn, 'click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // 分类过滤事件 - 使用事件委托处理动态添加的按钮
        const categoryFilters = DOMHelper.getElement('.category-filters');
        if (categoryFilters) {
            DOMHelper.addEventListener(categoryFilters, 'click', (e) => {
                if (e.target.classList.contains('category-btn')) {
                    this.setCategory(e.target.dataset.category);
                }
            });
        }

        // 优先级过滤事件
        this.elements.priorityBtns.forEach(btn => {
            DOMHelper.addEventListener(btn, 'click', (e) => {
                this.setPriority(e.target.dataset.priority);
            });
        });
    }

    /**
     * 初始化自定义分类
     */
    initCustomCategories() {
        this.updateCategoryFilters();
    }

    /**
     * 加载自定义分类
     * @returns {Array} 自定义分类列表
     */
    loadCustomCategories() {
        try {
            const saved = localStorage.getItem('customCategories');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('加载自定义分类失败:', error);
            return [];
        }
    }

    /**
     * 保存自定义分类
     */
    saveCustomCategories() {
        try {
            localStorage.setItem('customCategories', JSON.stringify(this.customCategories));
        } catch (error) {
            console.error('保存自定义分类失败:', error);
        }
    }

    /**
     * 处理分类选择变化
     * @param {string} value - 选择的值
     */
    handleCategorySelectChange(value) {
        const customCategoryInput = DOMHelper.getElement('#customCategoryInput');

        if (value === 'custom') {
            customCategoryInput.style.display = 'block';
            customCategoryInput.focus();
        } else {
            customCategoryInput.style.display = 'none';
            customCategoryInput.value = '';
        }
    }

    /**
     * 处理自定义分类输入
     */
    handleCustomCategoryInput() {
        const customCategoryInput = DOMHelper.getElement('#customCategoryInput');
        const categorySelect = DOMHelper.getElement('#categorySelect');
        const customCategory = customCategoryInput.value.trim();

        if (customCategory && customCategory.length > 0) {
            // 添加到自定义分类列表
            if (!this.customCategories.includes(customCategory)) {
                this.customCategories.push(customCategory);
                this.saveCustomCategories();
                this.updateCategoryFilters();
                UIHelper.showMessage(`自定义分类"${customCategory}"已添加`);
            }

            // 更新选择器
            this.addCustomCategoryOption(customCategory);
            categorySelect.value = customCategory;
        }

        customCategoryInput.style.display = 'none';
        customCategoryInput.value = '';
    }

    /**
     * 添加自定义分类选项到选择器
     * @param {string} category - 分类名称
     */
    addCustomCategoryOption(category) {
        const categorySelect = DOMHelper.getElement('#categorySelect');
        const existingOption = categorySelect.querySelector(`option[value="${category}"]`);

        if (!existingOption) {
            const option = DOMHelper.createElement('option', {
                value: category
            }, category);

            // 在"自定义..."选项之前插入
            const customOption = categorySelect.querySelector('option[value="custom"]');
            categorySelect.insertBefore(option, customOption);
        }
    }

    /**
     * 更新分类过滤按钮
     */
    updateCategoryFilters() {
        const categoryFilters = DOMHelper.getElement('.category-filters');
        if (!categoryFilters) return;

        // 清除现有的自定义分类按钮
        const customBtns = categoryFilters.querySelectorAll('.category-btn.custom-category');
        customBtns.forEach(btn => btn.remove());

        // 添加自定义分类按钮
        this.customCategories.forEach(category => {
            const btn = DOMHelper.createElement('button', {
                className: 'category-btn custom-category',
                'data-category': category
            }, category);

            categoryFilters.appendChild(btn);
        });
    }

    /**
     * 设置搜索查询
     * @param {string} query - 搜索查询
     */
    setSearchQuery(query) {
        this.searchQuery = query.trim();
        this.updateSearchUI();
        this.render();

        // 跟踪搜索事件
        if (this.searchQuery.length > 0) {
            this.trackEvent(TRACKING_CONFIG.EVENTS.SEARCH_PERFORM, {
                queryLength: this.searchQuery.length,
                hasResults: this.getFilteredTodos().length > 0
            });
        }
    }

    /**
     * 清空搜索
     */
    clearSearch() {
        const searchInput = DOMHelper.getElement('#searchInput');
        if (searchInput) {
            searchInput.value = '';
            this.setSearchQuery('');
        }
    }

    /**
     * 更新搜索UI
     */
    updateSearchUI() {
        const searchInput = DOMHelper.getElement('#searchInput');
        const clearSearchBtn = DOMHelper.getElement('#clearSearchBtn');

        if (clearSearchBtn) {
            if (this.searchQuery.length > 0) {
                clearSearchBtn.classList.add('visible');
            } else {
                clearSearchBtn.classList.remove('visible');
            }
        }
    }

    /**
     * 添加待办事项
     */
    addTodo() {
        const text = this.elements.todoInput.value.trim();
        if (text === '') {
            UIHelper.showMessage('请输入待办事项内容', 'warning');
            return;
        }

        // 获取分类值，处理自定义分类
        let category = this.elements.categorySelect.value;
        const customCategoryInput = DOMHelper.getElement('#customCategoryInput');

        if (category === 'custom' && customCategoryInput.style.display !== 'none') {
            const customCategory = customCategoryInput.value.trim();
            if (customCategory) {
                category = customCategory;
                // 添加到自定义分类列表
                if (!this.customCategories.includes(customCategory)) {
                    this.customCategories.push(customCategory);
                    this.saveCustomCategories();
                    this.updateCategoryFilters();
                }
                this.addCustomCategoryOption(customCategory);
            } else {
                UIHelper.showMessage('请输入自定义分类名称', 'warning');
                return;
            }
        }

        const todo = {
            id: Date.now(),
            text: text,
            completed: false,
            priority: this.elements.prioritySelect.value,
            category: category,
            folder: this.currentFolder,
            reminder: this.elements.reminderInput.value || null,
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        this.todos.unshift(todo);
        this.saveTodos();
        this.clearInputs();
        this.render();

        // 跟踪添加事件
        this.trackEvent(TRACKING_CONFIG.EVENTS.TODO_ADD, {
            todoId: todo.id,
            category: todo.category,
            priority: todo.priority,
            hasReminder: !!todo.reminder,
            todoText: todo.text // 将在跟踪器中进行脱敏处理
        });

        UIHelper.showMessage('待办事项添加成功');
    }

    /**
     * 删除待办事项
     * @param {number} id - 待办事项ID
     */
    deleteTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        this.todos = this.todos.filter(todo => todo.id !== id);
        this.saveTodos();
        this.render();

        // 跟踪删除事件
        if (todo) {
            this.trackEvent(TRACKING_CONFIG.EVENTS.TODO_DELETE, {
                todoId: id,
                category: todo.category,
                priority: todo.priority,
                wasCompleted: todo.completed
            });
        }

        UIHelper.showMessage('待办事项已删除');
    }

    /**
     * 切换待办事项完成状态
     * @param {number} id - 待办事项ID
     */
    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            const wasCompleted = todo.completed;
            todo.completed = !todo.completed;
            todo.completedAt = todo.completed ? new Date().toISOString() : null;
            this.saveTodos();
            this.render();

            // 跟踪完成事件
            if (!wasCompleted && todo.completed) {
                this.trackEvent(TRACKING_CONFIG.EVENTS.TODO_COMPLETE, {
                    todoId: id,
                    category: todo.category,
                    priority: todo.priority,
                    timeToComplete: todo.completedAt ? new Date(todo.completedAt).getTime() - new Date(todo.createdAt).getTime() : null
                });
            }
        }
    }

    /**
     * 编辑待办事项
     * @param {number} id - 待办事项ID
     * @param {Object} updates - 更新数据
     */
    editTodo(id, updates) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            Object.assign(todo, updates);
            this.saveTodos();
            this.render();
            UIHelper.showMessage('待办事项已更新');
        }
    }

    /**
     * 设置过滤器
     * @param {string} filter - 过滤器类型
     */
    setFilter(filter) {
        this.currentFilter = filter;
        this.elements.filterBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.filter === filter) {
                btn.classList.add('active');
            }
        });
        this.render();
    }

    /**
     * 设置分类过滤
     * @param {string} category - 分类
     */
    setCategory(category) {
        this.currentCategory = category;
        this.elements.categoryBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.category === category) {
                btn.classList.add('active');
            }
        });
        this.render();
    }

    /**
     * 设置优先级过滤
     * @param {string} priority - 优先级
     */
    setPriority(priority) {
        this.currentPriority = priority;
        this.elements.priorityBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.priority === priority) {
                btn.classList.add('active');
            }
        });
        this.render();
    }

    /**
     * 设置当前文件夹
     * @param {string} folderId - 文件夹ID
     */
    setCurrentFolder(folderId) {
        this.currentFolder = folderId;
        this.render();
    }

    /**
     * 获取过滤后的待办事项
     * @returns {Array} 过滤后的待办事项列表
     */
    getFilteredTodos() {
        let filtered = this.todos;

        // 按文件夹过滤
        filtered = filtered.filter(todo => todo.folder === this.currentFolder);

        // 按状态过滤
        switch (this.currentFilter) {
            case 'active':
                filtered = filtered.filter(todo => !todo.completed);
                break;
            case 'completed':
                filtered = filtered.filter(todo => todo.completed);
                break;
        }

        // 按分类过滤
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(todo => todo.category === this.currentCategory);
        }

        // 按优先级过滤
        if (this.currentPriority !== 'all') {
            filtered = filtered.filter(todo => todo.priority === this.currentPriority);
        }

        // 按搜索查询过滤
        if (this.searchQuery && this.searchQuery.length > 0) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(todo =>
                todo.text.toLowerCase().includes(query)
            );
        }

        return filtered;
    }

    /**
     * 渲染待办事项列表
     */
    render() {
        const filteredTodos = this.getFilteredTodos();

        // 清空列表
        this.elements.todoList.innerHTML = '';

        // 显示搜索结果信息
        this.renderSearchResultsInfo(filteredTodos);

        if (filteredTodos.length === 0) {
            const emptyMessage = this.searchQuery ?
                `没有找到包含"${this.searchQuery}"的待办事项` :
                '暂无待办事项';
            this.elements.todoList.innerHTML = `<li class="empty-message">${emptyMessage}</li>`;
            this.updateStats();
            this.updateReminders();
            return;
        }

        // 渲染待办事项
        filteredTodos.forEach(todo => {
            const li = this.createTodoElement(todo);
            this.elements.todoList.appendChild(li);
        });

        this.updateStats();
        this.updateReminders();
    }

    /**
     * 渲染搜索结果信息
     * @param {Array} filteredTodos - 过滤后的待办事项
     */
    renderSearchResultsInfo(filteredTodos) {
        // 移除现有的搜索结果信息
        const existingInfo = DOMHelper.getElement('.search-results-info');
        if (existingInfo) {
            existingInfo.remove();
        }

        if (this.searchQuery && this.searchQuery.length > 0) {
            const totalTodos = this.todos.filter(todo => todo.folder === this.currentFolder).length;
            const resultCount = filteredTodos.length;

            const infoDiv = DOMHelper.createElement('div', {
                className: `search-results-info ${resultCount === 0 ? 'no-results' : ''}`
            });

            if (resultCount === 0) {
                infoDiv.textContent = `没有找到包含"${this.searchQuery}"的待办事项`;
            } else {
                infoDiv.textContent = `找到 ${resultCount} 个包含"${this.searchQuery}"的待办事项（共 ${totalTodos} 个）`;
            }

            // 在待办事项列表前插入
            this.elements.todoList.parentNode.insertBefore(infoDiv, this.elements.todoList);
        }
    }

    /**
     * 创建待办事项DOM元素
     * @param {Object} todo - 待办事项对象
     * @returns {Element} 待办事项DOM元素
     */
    createTodoElement(todo) {
        const li = DOMHelper.createElement('li', {
            className: `todo-item priority-${todo.priority} ${todo.completed ? 'completed' : ''}`
        });

        const reminderText = todo.reminder ? DateHelper.formatDateTime(todo.reminder) : '';
        const isOverdue = todo.reminder && DateHelper.isOverdue(todo.reminder) && !todo.completed;

        // 处理搜索高亮
        const highlightedText = this.highlightSearchText(todo.text);

        // 获取分类显示名称（支持自定义分类）
        const categoryName = TODO_CONFIG.CATEGORIES[todo.category] || todo.category;

        li.innerHTML = `
            <div class="todo-content">
                <input type="checkbox" class="todo-checkbox" ${todo.completed ? 'checked' : ''}>
                <span class="todo-text">${highlightedText}</span>
                <div class="todo-meta">
                    <span class="todo-category">${StringHelper.escapeHtml(categoryName)}</span>
                    ${reminderText ? `<span class="todo-reminder ${isOverdue ? 'overdue' : ''}">提醒: ${reminderText}</span>` : ''}
                </div>
            </div>
            <div class="todo-actions">
                <button class="delete-btn">删除</button>
            </div>
        `;

        // 绑定事件
        const checkbox = li.querySelector('.todo-checkbox');
        const deleteBtn = li.querySelector('.delete-btn');

        DOMHelper.addEventListener(checkbox, 'change', () => this.toggleTodo(todo.id));
        DOMHelper.addEventListener(deleteBtn, 'click', () => this.deleteTodo(todo.id));

        return li;
    }

    /**
     * 高亮搜索文本
     * @param {string} text - 原始文本
     * @returns {string} 高亮后的HTML文本
     */
    highlightSearchText(text) {
        if (!this.searchQuery || this.searchQuery.length === 0) {
            return StringHelper.escapeHtml(text);
        }

        const escapedText = StringHelper.escapeHtml(text);
        const escapedQuery = StringHelper.escapeHtml(this.searchQuery);

        // 使用正则表达式进行不区分大小写的替换
        const regex = new RegExp(`(${escapedQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return escapedText.replace(regex, '<span class="search-highlight">$1</span>');
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const total = this.todos.length;
        const active = this.todos.filter(todo => !todo.completed).length;
        const completed = this.todos.filter(todo => todo.completed).length;
        const overdue = this.todos.filter(todo => 
            todo.reminder && DateHelper.isOverdue(todo.reminder) && !todo.completed
        ).length;

        // 获取当前值用于动画
        const currentTotal = parseInt(this.elements.totalCount.textContent.match(/\d+/)?.[0] || 0);
        const currentActive = parseInt(this.elements.activeCount.textContent.match(/\d+/)?.[0] || 0);
        const currentCompleted = parseInt(this.elements.completedCount.textContent.match(/\d+/)?.[0] || 0);
        const currentOverdue = parseInt(this.elements.overdueCount.textContent.match(/\d+/)?.[0] || 0);

        // 使用动画更新数字
        UIHelper.animateNumber(this.elements.totalCount, currentTotal, total);
        UIHelper.animateNumber(this.elements.activeCount, currentActive, active);
        UIHelper.animateNumber(this.elements.completedCount, currentCompleted, completed);
        UIHelper.animateNumber(this.elements.overdueCount, currentOverdue, overdue);
    }

    /**
     * 更新提醒列表
     */
    updateReminders() {
        const now = new Date();
        const upcomingReminders = this.todos
            .filter(todo => !todo.completed && todo.reminder && new Date(todo.reminder) > now)
            .sort((a, b) => new Date(a.reminder) - new Date(b.reminder))
            .slice(0, 5);

        // 清空提醒列表
        this.elements.remindersList.innerHTML = '';

        if (upcomingReminders.length === 0) {
            this.elements.remindersList.innerHTML = '<div class="reminder-item">暂无即将到来的提醒</div>';
            return;
        }

        upcomingReminders.forEach(todo => {
            const reminderDiv = DOMHelper.createElement('div', { className: 'reminder-item' });
            reminderDiv.innerHTML = `
                <strong>${StringHelper.escapeHtml(todo.text)}</strong><br>
                <small>${DateHelper.formatDateTime(todo.reminder)}</small>
            `;
            this.elements.remindersList.appendChild(reminderDiv);
        });
    }

    /**
     * 检查提醒
     */
    checkReminders() {
        const now = new Date();
        const dueReminders = this.todos.filter(todo => 
            !todo.completed && 
            todo.reminder && 
            new Date(todo.reminder) <= now &&
            !this.reminders.includes(todo.id)
        );

        dueReminders.forEach(todo => {
            this.showReminderNotification(todo);
            this.reminders.push(todo.id);
        });
    }

    /**
     * 显示提醒通知
     * @param {Object} todo - 待办事项对象
     */
    showReminderNotification(todo) {
        const message = `提醒：${todo.text}\n时间：${DateHelper.formatDateTime(todo.reminder)}`;
        
        // 显示浏览器通知
        if (Notification.permission === 'granted') {
            new Notification('待办事项提醒', {
                body: message,
                icon: '/favicon.ico'
            });
        }

        // 显示应用内消息
        UIHelper.showMessage(`提醒：${todo.text}`, 'warning', 5000);
    }

    /**
     * 清空输入框
     */
    clearInputs() {
        this.elements.todoInput.value = '';
        this.elements.reminderInput.value = '';

        // 重置分类选择器和自定义分类输入
        const categorySelect = DOMHelper.getElement('#categorySelect');
        const customCategoryInput = DOMHelper.getElement('#customCategoryInput');

        if (categorySelect) {
            categorySelect.value = 'work'; // 默认选择工作分类
        }

        if (customCategoryInput) {
            customCategoryInput.style.display = 'none';
            customCategoryInput.value = '';
        }
    }

    /**
     * 保存待办事项到本地存储
     */
    saveTodos() {
        TodoStorage.save(this.todos);
    }

    /**
     * 获取所有待办事项
     * @returns {Array} 待办事项列表
     */
    getAllTodos() {
        return this.todos;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息对象
     */
    getStats() {
        return {
            total: this.todos.length,
            active: this.todos.filter(todo => !todo.completed).length,
            completed: this.todos.filter(todo => todo.completed).length,
            overdue: this.todos.filter(todo => 
                todo.reminder && DateHelper.isOverdue(todo.reminder) && !todo.completed
            ).length
        };
    }

    /**
     * 导出待办事项数据
     * @param {string} format - 导出格式 (json|csv)
     * @returns {string} 导出的数据
     */
    exportData(format = 'json') {
        switch (format) {
            case 'json':
                return JSON.stringify(this.todos, null, 2);
            case 'csv':
                const headers = ['ID', '内容', '完成状态', '优先级', '分类', '文件夹', '提醒时间', '创建时间', '完成时间'];
                const rows = this.todos.map(todo => [
                    todo.id,
                    todo.text,
                    todo.completed ? '已完成' : '未完成',
                    DataHelper.getPriorityName(todo.priority),
                    DataHelper.getCategoryName(todo.category),
                    todo.folder,
                    todo.reminder || '',
                    todo.createdAt,
                    todo.completedAt || ''
                ]);
                return [headers, ...rows].map(row => row.join(',')).join('\n');
            default:
                return JSON.stringify(this.todos, null, 2);
        }
    }

    /**
     * 导入待办事项数据
     * @param {string} data - 导入的数据
     * @param {string} format - 数据格式 (json)
     * @returns {boolean} 是否导入成功
     */
    importData(data, format = 'json') {
        try {
            if (format === 'json') {
                const importedTodos = JSON.parse(data);
                if (Array.isArray(importedTodos)) {
                    this.todos = importedTodos;
                    this.saveTodos();
                    this.render();
                    UIHelper.showMessage('数据导入成功');
                    return true;
                }
            }
            throw new Error('数据格式不正确');
        } catch (error) {
            UIHelper.showMessage('数据导入失败：' + error.message, 'error');
            return false;
        }
    }

    /**
     * 设置用户跟踪器
     * @param {UserTracker} userTracker - 用户跟踪器实例
     */
    setUserTracker(userTracker) {
        this.userTracker = userTracker;
    }

    /**
     * 跟踪事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    trackEvent(eventType, eventData = {}) {
        if (this.userTracker) {
            this.userTracker.trackEvent(eventType, eventData);
        }
    }
}
