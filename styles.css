* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: white;
    --text-primary: #333;
    --text-secondary: #666;
    --border-color: #ddd;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --hover-bg: #f8f9fa;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --primary-gradient: linear-gradient(45deg, #667eea, #764ba2);
    --clock-border: #667eea;
    --weather-bg: linear-gradient(135deg, #87CEEB 0%, #98D8E8 100%);
    --reminder-bg: #fff3cd;
    --reminder-border: #ffeaa7;
    --reminder-text: #856404;
}

[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --bg-secondary: #2d2d44;
    --text-primary: #e4e4e7;
    --text-secondary: #a1a1aa;
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --hover-bg: #404040;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --primary-gradient: linear-gradient(45deg, #8b5cf6, #a855f7);
    --clock-border: #8b5cf6;
    --weather-bg: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    --reminder-bg: #451a03;
    --reminder-border: #92400e;
    --reminder-text: #fef3c7;
}

body {
    font-family: 'Arial', sans-serif;
    background: var(--bg-primary);
    min-height: 100vh;
    padding: 20px;
    color: var(--text-primary);
    transition: background-color 0.5s ease, color 0.5s ease;
}

/* 主题切换按钮 */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.theme-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.theme-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px var(--shadow-color);
}

.theme-btn:active {
    transform: scale(0.95);
}

.theme-icon, .settings-icon {
    transition: transform 0.3s ease;
}

.theme-btn:hover .theme-icon {
    transform: rotate(180deg);
}

.theme-btn:hover .settings-icon {
    transform: rotate(30deg);
}

.main-container {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    transition: all 0.5s ease;
}

/* 时钟样式 */
.clock-section {
    background: var(--bg-secondary);
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow-color);
    padding: 20px;
    text-align: center;
    width: 250px;
    height: fit-content;
    transition: all 0.5s ease;
}

.analog-clock {
    width: 200px;
    height: 200px;
    border: 8px solid var(--clock-border);
    border-radius: 50%;
    position: relative;
    margin: 0 auto 20px;
    background: var(--bg-secondary);
}

.clock-face {
    width: 100%;
    height: 100%;
    position: relative;
}

.hand {
    position: absolute;
    background: var(--text-primary);
    transform-origin: bottom center;
    left: 50%;
    bottom: 50%;
    border-radius: 2px;
}

.hour-hand {
    width: 4px;
    height: 60px;
    margin-left: -2px;
}

.minute-hand {
    width: 3px;
    height: 80px;
    margin-left: -1.5px;
}

.second-hand {
    width: 2px;
    height: 90px;
    margin-left: -1px;
    background: var(--error-color);
}

.center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.digital-time {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-primary);
    margin-top: 10px;
}

/* 天气显示样式 */
.weather-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.weather-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 15px;
}

.weather-select-row {
    display: flex;
    gap: 10px;
    align-items: center;
}

.weather-refresh-row {
    display: flex;
    justify-content: center;
}

#countrySelect, #regionSelect {
    flex: 1;
    padding: 8px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 12px;
    outline: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#countrySelect:focus, #regionSelect:focus {
    border-color: var(--clock-border);
}

#refreshWeather {
    padding: 8px 12px;
    background: var(--clock-border);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

#refreshWeather:hover {
    background: var(--primary-gradient);
}

.weather-display {
    background: var(--weather-bg);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.8s ease;
}

.weather-display.sunny {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.weather-display.cloudy {
    background: linear-gradient(135deg, #B0C4DE 0%, #778899 100%);
}

.weather-display.overcast {
    background: linear-gradient(135deg, #696969 0%, #2F4F4F 100%);
}

.weather-display.rainy {
    background: linear-gradient(135deg, #4682B4 0%, #191970 100%);
}

.weather-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.3;
    animation: weatherBackgroundChange 10s ease-in-out infinite;
}

.weather-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    z-index: 2;
}

.weather-scene {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.weather-element {
    position: absolute;
    font-size: 20px;
    transition: all 0.8s ease;
}

.sun {
    top: 10px;
    right: 10px;
    color: #FFD700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
}

.cloud {
    color: #FFFFFF;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.cloud-1 {
    top: 15px;
    left: 20px;
}

.cloud-2 {
    top: 25px;
    right: 30px;
}

.rain-drop {
    color: #4682B4;
    font-size: 14px;
    opacity: 0;
}

.weather-transition .sun-exit {
    animation: sunToCloudy 1s ease-out forwards;
}

.weather-transition .cloud-enter {
    animation: cloudEnter 1s ease-out forwards;
}

.weather-transition .cloud-gray {
    animation: cloudToGray 1s ease-out forwards;
}

.weather-transition .rain-fall {
    animation: rainDrop 1s ease-in-out infinite;
}

.weather-main {
    display: flex;
    align-items: center;
    gap: 10px;
}

.weather-icon {
    font-size: 24px;
}

.weather-temp {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-primary);
}

.weather-desc {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: capitalize;
}

.weather-details {
    display: flex;
    gap: 15px;
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 5px;
}

.weather-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.weather-loading {
    color: var(--text-secondary);
    font-style: italic;
}

.weather-error {
    color: var(--error-color);
    font-size: 12px;
    text-align: center;
}

.container {
    flex: 1;
    background: var(--bg-secondary);
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow-color);
    padding: 30px;
    transition: all 0.5s ease;
}

h1 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 文件夹管理 */
.folder-section {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
}

#folderSelect {
    flex: 1;
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.5s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#addFolderBtn {
    padding: 10px 15px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

#addFolderBtn:hover {
    background: var(--success-color);
    filter: brightness(1.1);
}

/* 增强的输入区域 */
.enhanced-input-section {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
}

#todoInput {
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    transition: all 0.5s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#todoInput:focus {
    border-color: var(--clock-border);
}

#prioritySelect, #categorySelect {
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.5s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#reminderInput {
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.5s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#addBtn {
    padding: 12px 25px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

#addBtn:hover {
    transform: translateY(-2px);
}

/* 过滤区域 */
.filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.status-filters, .category-filters, .priority-filters {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn, .category-btn, .priority-btn {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: var(--text-primary);
}

.filter-btn.active, .category-btn.active, .priority-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: var(--clock-border);
}

.filter-btn:hover, .category-btn:hover, .priority-btn:hover {
    border-color: var(--clock-border);
}

/* 提醒区域 */
.reminders-section {
    background: var(--reminder-bg);
    border: 1px solid var(--reminder-border);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.reminders-section h3 {
    color: var(--reminder-text);
    margin-bottom: 10px;
    font-size: 16px;
}

#remindersList {
    font-size: 14px;
    color: var(--reminder-text);
}

.reminder-item {
    background: var(--bg-secondary);
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 3px solid var(--warning-color);
}

.todo-list {
    list-style: none;
    margin-bottom: 20px;
}

.todo-item {
    background: var(--hover-bg);
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.5s ease;
    border-left: 4px solid var(--clock-border);
}

.todo-item.priority-critical {
    border-left-color: #8b0000;
}

.todo-item.priority-high {
    border-left-color: var(--error-color);
}

.todo-item.priority-medium {
    border-left-color: var(--warning-color);
}

.todo-item.priority-low {
    border-left-color: var(--success-color);
}

.todo-item.priority-lowest {
    border-left-color: #90ee90;
}

.todo-item.completed {
    opacity: 0.7;
    border-left-color: var(--success-color);
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: var(--text-secondary);
}

.todo-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.todo-checkbox {
    margin-right: 15px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.todo-text {
    flex: 1;
    font-size: 16px;
    color: var(--text-primary);
}

.todo-meta {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: 15px;
    font-size: 12px;
    color: var(--text-secondary);
}

.todo-category {
    background: var(--hover-bg);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    display: inline-block;
    color: var(--text-secondary);
}

.todo-reminder {
    color: var(--error-color);
    font-weight: bold;
}

.todo-actions {
    display: flex;
    gap: 10px;
}

.delete-btn {
    background: var(--error-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.delete-btn:hover {
    background: var(--error-color);
    filter: brightness(1.1);
}

.stats {
    display: flex;
    justify-content: space-between;
    background: var(--hover-bg);
    padding: 15px;
    border-radius: 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

.stats span {
    font-weight: bold;
}

#overdueCount {
    color: var(--error-color);
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }
    
    .clock-section {
        width: 100%;
        order: 2;
    }
    
    .container {
        order: 1;
    }
    
    .enhanced-input-section {
        grid-template-columns: 1fr;
    }
    
    .status-filters, .category-filters, .priority-filters {
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .stats {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes numberChange {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
        color: #667eea;
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes weatherIconChange {
    0% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
    }
}

@keyframes sunToCloudy {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-40px) scale(0.8);
        opacity: 0;
    }
}

@keyframes cloudEnter {
    0% {
        transform: translateY(40px) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translateY(20px) scale(0.9);
        opacity: 0.6;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes cloudToGray {
    0% {
        filter: brightness(1);
        transform: scale(1);
    }
    50% {
        filter: brightness(0.8);
        transform: scale(1.05);
    }
    100% {
        filter: brightness(0.6) grayscale(0.5);
        transform: scale(1);
    }
}

@keyframes rainDrop {
    0% {
        transform: translateY(-10px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(30px);
        opacity: 0;
    }
}

@keyframes weatherBackgroundChange {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 页面加载动画 */
.main-container {
    animation: fadeIn 0.8s ease-out;
}

.container {
    animation: slideInLeft 0.6s ease-out 0.2s both;
}

.clock-section {
    animation: slideInRight 0.6s ease-out 0.2s both;
}

/* 天气组件动画 */
.weather-section {
    animation: fadeIn 0.6s ease-out 0.4s both;
}

.weather-info {
    animation: scaleIn 0.5s ease-out;
}

.weather-icon {
    transition: all 0.3s ease;
}

.weather-icon.animated {
    animation: weatherIconChange 0.6s ease-in-out;
}

.weather-temp {
    transition: all 0.3s ease;
    display: inline-block;
}

.weather-temp.changing {
    animation: numberChange 0.5s ease-in-out;
}

.weather-desc {
    animation: fadeIn 0.4s ease-out 0.2s both;
}

.weather-detail {
    animation: fadeIn 0.3s ease-out 0.3s both;
}

/* 刷新按钮动画 */
#refreshWeather {
    position: relative;
    overflow: hidden;
}

#refreshWeather.loading {
    animation: pulse 1s infinite;
}

#refreshWeather::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

#refreshWeather:active::after {
    width: 100px;
    height: 100px;
}

/* 选择器动画 */
#countrySelect, #regionSelect {
    transition: all 0.3s ease;
}

#countrySelect:focus, #regionSelect:focus {
    animation: pulse 0.3s ease-in-out;
}

/* 待办事项动画 */
.todo-item {
    animation: slideInLeft 0.4s ease-out;
    transition: all 0.3s ease;
}

.todo-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.todo-item.completed {
    animation: slideInRight 0.4s ease-out;
}

/* 按钮动画 */
#addBtn, #addFolderBtn, .delete-btn, .filter-btn, .category-btn, .priority-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#addBtn:hover, #addFolderBtn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.delete-btn:hover {
    transform: scale(1.1);
}

.filter-btn:hover, .category-btn:hover, .priority-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* 输入框动画 */
#todoInput, #prioritySelect, #categorySelect, #reminderInput, #folderSelect {
    transition: all 0.3s ease;
}

#todoInput:focus, #prioritySelect:focus, #categorySelect:focus, #reminderInput:focus, #folderSelect:focus {
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

/* 统计数字动画 */
.stats span {
    transition: all 0.3s ease;
    display: inline-block;
}

.stats span.updating {
    animation: numberChange 0.5s ease-in-out;
}

/* 提醒项目动画 */
.reminder-item {
    animation: fadeIn 0.4s ease-out;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* 时钟指针动画 */
.hand {
    transition: transform 0.5s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 消息动画 */
.message {
    animation: slideInRight 0.3s ease-out;
}

/* 加载动画 */
.weather-loading {
    position: relative;
}

.weather-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: rotate 1s linear infinite;
}

/* 数字时钟动画 */
.digital-time {
    transition: all 0.3s ease;
}

.digital-time.updating {
    animation: numberChange 0.3s ease-in-out;
}

/* 设置页面样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--bg-secondary);
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px var(--shadow-color);
    animation: slideInDown 0.3s ease;
    transition: all 0.5s ease;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.close {
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--error-color);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.settings-section {
    margin-bottom: 30px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid var(--clock-border);
    padding-bottom: 5px;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: bold;
}

.setting-item input[type="password"],
.setting-item input[type="text"],
.setting-item select {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.setting-item input[type="password"]:focus,
.setting-item input[type="text"]:focus,
.setting-item select:focus {
    border-color: var(--clock-border);
    outline: none;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.setting-item input[type="checkbox"] {
    margin-right: 10px;
}

.setting-help {
    margin-top: 10px;
    padding: 10px;
    background: var(--hover-bg);
    border-radius: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.setting-help p {
    margin: 0 0 5px 0;
}

.setting-help ol {
    margin: 0;
    padding-left: 20px;
}

.setting-help li {
    margin-bottom: 3px;
}

.setting-help a {
    color: var(--clock-border);
    text-decoration: none;
}

.setting-help a:hover {
    text-decoration: underline;
}

.api-status {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--hover-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    background: var(--text-secondary);
    transition: all 0.3s ease;
}

.status-indicator.success {
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-indicator.error {
    background: var(--error-color);
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.status-indicator.testing {
    background: var(--warning-color);
    animation: pulse 1s infinite;
}

.status-text {
    font-weight: bold;
    color: var(--text-primary);
}

.setting-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.setting-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

#testApiBtn {
    background: var(--warning-color);
    color: white;
}

#testApiBtn:hover {
    background: var(--warning-color);
    filter: brightness(1.1);
    transform: translateY(-2px);
}

#saveSettingsBtn {
    background: var(--success-color);
    color: white;
}

#saveSettingsBtn:hover {
    background: var(--success-color);
    filter: brightness(1.1);
    transform: translateY(-2px);
}

#resetSettingsBtn {
    background: var(--error-color);
    color: white;
}

#resetSettingsBtn:hover {
    background: var(--error-color);
    filter: brightness(1.1);
    transform: translateY(-2px);
}

/* 设置按钮动画 */
#settingsToggle {
    margin-right: 10px;
}

#settingsToggle:hover .theme-icon {
    transform: rotate(30deg);
}

/* 模态框动画 */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .setting-actions {
        flex-direction: column;
    }
    
    .setting-actions button {
        width: 100%;
    }
}

/* 模块化应用支持样式 */
.empty-message {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 40px 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    margin: 20px 0;
    border: 2px dashed var(--border-color);
}

.context-menu {
    position: fixed;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px var(--shadow-color);
    z-index: 10000;
    min-width: 120px;
    padding: 4px 0;
}

.context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-primary);
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background-color: var(--hover-bg);
}

.context-menu-item.danger {
    color: var(--error-color);
}

/* 主题过渡效果 */
.theme-transitioning {
    transition: all 0.5s ease;
}

.theme-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    opacity: 0;
    z-index: 9999;
    pointer-events: none;
    transition: opacity 0.25s ease-in-out;
}

/* 数字动画效果 */
.updating {
    animation: numberUpdate 0.3s ease-out;
}

@keyframes numberUpdate {
    0% {
        transform: scale(1.1);
        color: var(--success-color);
    }
    100% {
        transform: scale(1);
        color: var(--text-primary);
    }
}

/* 搜索功能样式 */
.search-section {
    margin-bottom: 20px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 400px;
}

#searchInput {
    flex: 1;
    padding: 12px 40px 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

#searchInput:focus {
    border-color: var(--clock-border);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#searchInput::placeholder {
    color: var(--text-secondary);
}

.clear-search-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    font-size: 12px;
    line-height: 1;
    transition: all 0.2s ease;
    display: none;
}

.clear-search-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.clear-search-btn.visible {
    display: block;
}

/* 自定义分类输入样式 */
.category-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

#customCategoryInput {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    min-width: 120px;
}

#customCategoryInput:focus {
    border-color: var(--clock-border);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

#customCategoryInput::placeholder {
    color: var(--text-secondary);
}

/* 搜索高亮样式 */
.search-highlight {
    background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
    color: #2d3436;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: bold;
}

[data-theme="dark"] .search-highlight {
    background: linear-gradient(120deg, #f39c12 0%, #e67e22 100%);
    color: #2c3e50;
}

/* 动态分类按钮样式 */
.category-btn.custom-category {
    background: linear-gradient(45deg, #a29bfe, #6c5ce7);
    color: white;
    border: none;
}

.category-btn.custom-category:hover {
    background: linear-gradient(45deg, #6c5ce7, #5f3dc4);
    transform: translateY(-1px);
}

.category-btn.custom-category.active {
    background: linear-gradient(45deg, #5f3dc4, #4c63d2);
    box-shadow: 0 4px 15px rgba(108, 92, 231, 0.4);
}

/* 搜索结果提示 */
.search-results-info {
    margin-bottom: 10px;
    padding: 8px 12px;
    background: var(--reminder-bg);
    border: 1px solid var(--reminder-border);
    border-radius: 6px;
    color: var(--reminder-text);
    font-size: 14px;
    text-align: center;
}

.search-results-info.no-results {
    background: #ffe6e6;
    border-color: #ffb3b3;
    color: #cc0000;
}

[data-theme="dark"] .search-results-info.no-results {
    background: #4a1a1a;
    border-color: #663333;
    color: #ff6666;
}
