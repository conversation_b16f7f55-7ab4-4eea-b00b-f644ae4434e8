/**
 * 数据分析模块
 * @fileoverview 负责用户数据分析、统计和可视化
 */

import { Storage } from '../utils/storage.js';
import { STORAGE_KEYS, ANALYTICS_CONFIG, TRACKING_CONFIG } from '../constants/config.js';
import { DateHelper } from '../utils/helpers.js';

/**
 * 数据分析器
 */
export class DataAnalytics {
    /**
     * 构造函数
     */
    constructor() {
        this.analytics = Storage.load(STORAGE_KEYS.userAnalytics, []);
        this.sessions = Storage.load(STORAGE_KEYS.userSessions, []);
    }

    /**
     * 获取用户概览统计
     * @returns {Object} 用户概览数据
     */
    getUserOverview() {
        const uniqueUsers = new Set(this.analytics.map(event => event.userId)).size;
        const totalSessions = this.sessions.length;
        const totalEvents = this.analytics.length;
        
        // 活跃用户（最近7天有活动）
        const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const activeUsers = new Set(
            this.analytics
                .filter(event => event.timestamp > sevenDaysAgo)
                .map(event => event.userId)
        ).size;
        
        // 今日活跃用户
        const todayStart = DateHelper.getTodayStart().getTime();
        const todayActiveUsers = new Set(
            this.analytics
                .filter(event => event.timestamp > todayStart)
                .map(event => event.userId)
        ).size;
        
        return {
            totalUsers: uniqueUsers,
            activeUsers7d: activeUsers,
            todayActiveUsers: todayActiveUsers,
            totalSessions: totalSessions,
            totalEvents: totalEvents,
            avgEventsPerUser: uniqueUsers > 0 ? Math.round(totalEvents / uniqueUsers) : 0,
            avgSessionsPerUser: uniqueUsers > 0 ? Math.round(totalSessions / uniqueUsers) : 0
        };
    }

    /**
     * 获取用户列表
     * @param {Object} options - 查询选项
     * @returns {Array} 用户列表
     */
    getUserList(options = {}) {
        const { sortBy = 'lastSeen', sortOrder = 'desc', limit = 100 } = options;
        
        // 按用户ID分组统计
        const userStats = {};
        
        // 统计事件数据
        this.analytics.forEach(event => {
            if (!userStats[event.userId]) {
                userStats[event.userId] = {
                    userId: event.userId,
                    firstSeen: event.timestamp,
                    lastSeen: event.timestamp,
                    eventCount: 0,
                    sessionIds: new Set(),
                    eventTypes: {}
                };
            }
            
            const user = userStats[event.userId];
            user.eventCount++;
            user.firstSeen = Math.min(user.firstSeen, event.timestamp);
            user.lastSeen = Math.max(user.lastSeen, event.timestamp);
            user.sessionIds.add(event.sessionId);
            user.eventTypes[event.type] = (user.eventTypes[event.type] || 0) + 1;
        });
        
        // 添加会话信息
        this.sessions.forEach(session => {
            if (userStats[session.userId]) {
                userStats[session.userId].ipAddress = session.ipAddress;
                userStats[session.userId].userAgent = session.userAgent;
                userStats[session.userId].fingerprint = session.fingerprint;
            }
        });
        
        // 转换为数组并添加计算字段
        const users = Object.values(userStats).map(user => ({
            ...user,
            sessionCount: user.sessionIds.size,
            daysSinceFirstSeen: Math.floor((Date.now() - user.firstSeen) / (24 * 60 * 60 * 1000)),
            daysSinceLastSeen: Math.floor((Date.now() - user.lastSeen) / (24 * 60 * 60 * 1000)),
            avgEventsPerSession: user.sessionIds.size > 0 ? Math.round(user.eventCount / user.sessionIds.size) : 0
        }));
        
        // 排序
        users.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];
            
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (sortOrder === 'desc') {
                return bValue > aValue ? 1 : -1;
            } else {
                return aValue > bValue ? 1 : -1;
            }
        });
        
        return users.slice(0, limit);
    }

    /**
     * 获取活跃度趋势数据
     * @param {string} dimension - 时间维度 (daily/weekly/monthly)
     * @param {number} periods - 时间段数量
     * @returns {Object} 趋势数据
     */
    getActivityTrend(dimension = ANALYTICS_CONFIG.DIMENSIONS.DAILY, periods = 30) {
        const now = new Date();
        const trends = [];
        
        for (let i = periods - 1; i >= 0; i--) {
            let periodStart, periodEnd, label;
            
            switch (dimension) {
                case ANALYTICS_CONFIG.DIMENSIONS.DAILY:
                    periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - i);
                    periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);
                    label = periodStart.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
                    break;
                    
                case ANALYTICS_CONFIG.DIMENSIONS.WEEKLY:
                    const weekStart = new Date(now.getTime() - (i * 7 * 24 * 60 * 60 * 1000));
                    periodStart = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() - weekStart.getDay());
                    periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);
                    label = `${periodStart.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}`;
                    break;
                    
                case ANALYTICS_CONFIG.DIMENSIONS.MONTHLY:
                    periodStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
                    periodEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
                    label = periodStart.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short' });
                    break;
            }
            
            const periodEvents = this.analytics.filter(event => 
                event.timestamp >= periodStart.getTime() && event.timestamp < periodEnd.getTime()
            );
            
            const uniqueUsers = new Set(periodEvents.map(event => event.userId)).size;
            const totalEvents = periodEvents.length;
            
            trends.push({
                period: label,
                timestamp: periodStart.getTime(),
                activeUsers: uniqueUsers,
                totalEvents: totalEvents,
                avgEventsPerUser: uniqueUsers > 0 ? Math.round(totalEvents / uniqueUsers) : 0
            });
        }
        
        return {
            dimension,
            periods,
            data: trends
        };
    }

    /**
     * 获取事件类型分布
     * @returns {Object} 事件类型统计
     */
    getEventTypeDistribution() {
        const distribution = {};
        const eventTypeNames = {
            [TRACKING_CONFIG.EVENTS.APP_START]: '应用启动',
            [TRACKING_CONFIG.EVENTS.TODO_ADD]: '添加待办',
            [TRACKING_CONFIG.EVENTS.TODO_COMPLETE]: '完成待办',
            [TRACKING_CONFIG.EVENTS.TODO_DELETE]: '删除待办',
            [TRACKING_CONFIG.EVENTS.THEME_CHANGE]: '主题切换',
            [TRACKING_CONFIG.EVENTS.WEATHER_UPDATE]: '天气更新',
            [TRACKING_CONFIG.EVENTS.SETTINGS_CHANGE]: '设置变更',
            [TRACKING_CONFIG.EVENTS.FOLDER_CREATE]: '创建文件夹',
            [TRACKING_CONFIG.EVENTS.SEARCH_PERFORM]: '执行搜索'
        };
        
        this.analytics.forEach(event => {
            const eventName = eventTypeNames[event.type] || event.type;
            distribution[eventName] = (distribution[eventName] || 0) + 1;
        });
        
        // 转换为图表数据格式
        const chartData = Object.entries(distribution).map(([name, count]) => ({
            name,
            value: count,
            percentage: Math.round((count / this.analytics.length) * 100)
        }));
        
        // 按数量排序
        chartData.sort((a, b) => b.value - a.value);
        
        return {
            total: this.analytics.length,
            distribution: chartData
        };
    }

    /**
     * 获取用户留存率
     * @returns {Object} 留存率数据
     */
    getUserRetention() {
        const userFirstSeen = {};
        
        // 记录每个用户的首次访问时间
        this.analytics.forEach(event => {
            if (!userFirstSeen[event.userId] || event.timestamp < userFirstSeen[event.userId]) {
                userFirstSeen[event.userId] = event.timestamp;
            }
        });
        
        const cohorts = {};
        const now = Date.now();
        
        // 按首次访问日期分组用户
        Object.entries(userFirstSeen).forEach(([userId, firstSeen]) => {
            const cohortDate = new Date(firstSeen);
            cohortDate.setHours(0, 0, 0, 0);
            const cohortKey = cohortDate.toISOString().split('T')[0];
            
            if (!cohorts[cohortKey]) {
                cohorts[cohortKey] = [];
            }
            cohorts[cohortKey].push(userId);
        });
        
        // 计算留存率
        const retentionData = [];
        
        Object.entries(cohorts).forEach(([cohortDate, users]) => {
            const cohortStart = new Date(cohortDate).getTime();
            const daysSinceCohort = Math.floor((now - cohortStart) / (24 * 60 * 60 * 1000));
            
            if (daysSinceCohort >= 1) {
                // 计算第1天、第7天、第30天留存
                const retention = {
                    cohortDate,
                    cohortSize: users.length,
                    day1: 0,
                    day7: 0,
                    day30: 0
                };
                
                users.forEach(userId => {
                    const userEvents = this.analytics.filter(event => event.userId === userId);
                    const day1Events = userEvents.filter(event => 
                        event.timestamp >= cohortStart + 24 * 60 * 60 * 1000 &&
                        event.timestamp < cohortStart + 2 * 24 * 60 * 60 * 1000
                    );
                    const day7Events = userEvents.filter(event => 
                        event.timestamp >= cohortStart + 7 * 24 * 60 * 60 * 1000 &&
                        event.timestamp < cohortStart + 8 * 24 * 60 * 60 * 1000
                    );
                    const day30Events = userEvents.filter(event => 
                        event.timestamp >= cohortStart + 30 * 24 * 60 * 60 * 1000 &&
                        event.timestamp < cohortStart + 31 * 24 * 60 * 60 * 1000
                    );
                    
                    if (day1Events.length > 0) retention.day1++;
                    if (day7Events.length > 0) retention.day7++;
                    if (day30Events.length > 0) retention.day30++;
                });
                
                retention.day1Percent = Math.round((retention.day1 / retention.cohortSize) * 100);
                retention.day7Percent = Math.round((retention.day7 / retention.cohortSize) * 100);
                retention.day30Percent = Math.round((retention.day30 / retention.cohortSize) * 100);
                
                retentionData.push(retention);
            }
        });
        
        return retentionData.sort((a, b) => new Date(b.cohortDate) - new Date(a.cohortDate));
    }

    /**
     * 获取时间段活跃度热力图数据
     * @returns {Object} 热力图数据
     */
    getHourlyHeatmap() {
        const heatmapData = Array(7).fill().map(() => Array(24).fill(0));
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        
        this.analytics.forEach(event => {
            const date = new Date(event.timestamp);
            const dayOfWeek = date.getDay();
            const hour = date.getHours();
            heatmapData[dayOfWeek][hour]++;
        });
        
        // 找出最大值用于归一化
        const maxValue = Math.max(...heatmapData.flat());
        
        return {
            data: heatmapData,
            maxValue,
            dayNames,
            hours: Array.from({ length: 24 }, (_, i) => i)
        };
    }

    /**
     * 导出数据
     * @param {string} format - 导出格式 (json/csv)
     * @param {Object} options - 导出选项
     * @returns {string} 导出的数据
     */
    exportData(format = 'json', options = {}) {
        const { includeAnalytics = true, includeSessions = true, sanitize = true } = options;
        
        const exportData = {};
        
        if (includeAnalytics) {
            exportData.analytics = sanitize ? 
                this.analytics.map(event => this.sanitizeEvent(event)) : 
                this.analytics;
        }
        
        if (includeSessions) {
            exportData.sessions = sanitize ? 
                this.sessions.map(session => this.sanitizeSession(session)) : 
                this.sessions;
        }
        
        exportData.exportTime = new Date().toISOString();
        exportData.totalRecords = (exportData.analytics?.length || 0) + (exportData.sessions?.length || 0);
        
        if (format === 'csv') {
            return this.convertToCSV(exportData);
        } else {
            return JSON.stringify(exportData, null, 2);
        }
    }

    /**
     * 事件数据脱敏
     * @param {Object} event - 原始事件
     * @returns {Object} 脱敏后的事件
     */
    sanitizeEvent(event) {
        const sanitized = { ...event };
        
        // 移除或脱敏敏感信息
        if (sanitized.data && sanitized.data.todoText) {
            sanitized.data.todoText = `[${sanitized.data.todoText.length} chars]`;
        }
        
        if (sanitized.data && sanitized.data.userAgent) {
            sanitized.data.userAgent = sanitized.data.userAgent.substring(0, 50) + '...';
        }
        
        return sanitized;
    }

    /**
     * 会话数据脱敏
     * @param {Object} session - 原始会话
     * @returns {Object} 脱敏后的会话
     */
    sanitizeSession(session) {
        const sanitized = { ...session };
        
        if (sanitized.ipAddress) {
            const ipParts = sanitized.ipAddress.split('.');
            if (ipParts.length === 4) {
                sanitized.ipAddress = `${ipParts[0]}.${ipParts[1]}.xxx.xxx`;
            }
        }
        
        if (sanitized.userAgent) {
            sanitized.userAgent = sanitized.userAgent.substring(0, 100) + '...';
        }
        
        return sanitized;
    }

    /**
     * 转换为CSV格式
     * @param {Object} data - 要转换的数据
     * @returns {string} CSV字符串
     */
    convertToCSV(data) {
        let csv = '';
        
        if (data.analytics) {
            csv += 'Analytics Data\n';
            csv += 'ID,Type,User ID,Session ID,Timestamp,Data\n';
            data.analytics.forEach(event => {
                csv += `${event.id},${event.type},${event.userId},${event.sessionId},${new Date(event.timestamp).toISOString()},"${JSON.stringify(event.data).replace(/"/g, '""')}"\n`;
            });
            csv += '\n';
        }
        
        if (data.sessions) {
            csv += 'Session Data\n';
            csv += 'ID,User ID,Start Time,IP Address,User Agent\n';
            data.sessions.forEach(session => {
                csv += `${session.id},${session.userId},${new Date(session.startTime).toISOString()},${session.ipAddress},"${session.userAgent.replace(/"/g, '""')}"\n`;
            });
        }
        
        return csv;
    }
}
