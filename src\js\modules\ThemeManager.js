/**
 * 主题管理模块
 * @fileoverview 负责应用主题的切换和管理
 */

import { SettingsStorage } from '../utils/storage.js';
import { DOMHelper, UIHelper } from '../utils/helpers.js';
import { DEFAULT_SETTINGS, STORAGE_KEYS } from '../constants/config.js';

/**
 * 主题管理器
 */
export class ThemeManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     */
    constructor(elements) {
        this.elements = elements;
        this.currentTheme = DEFAULT_SETTINGS.theme;
        this.themes = this.getAvailableThemes();
        this.transitionDuration = 500; // 主题切换动画时长
        
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        this.loadSavedTheme();
        this.bindEvents();
        this.updateThemeIcon();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主题切换按钮事件
        DOMHelper.addEventListener(this.elements.themeToggle, 'click', () => {
            this.toggleTheme();
        });

        // 监听系统主题变化
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                this.onSystemThemeChange(e.matches);
            });
        }
    }

    /**
     * 加载保存的主题
     */
    loadSavedTheme() {
        const savedTheme = SettingsStorage.load(STORAGE_KEYS.theme, DEFAULT_SETTINGS.theme);
        this.setTheme(savedTheme, false);
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme, true);
        
        const themeName = newTheme === 'dark' ? '黑暗' : '明亮';
        UIHelper.showMessage(`已切换到${themeName}模式`);
    }

    /**
     * 设置主题
     * @param {string} theme - 主题名称
     * @param {boolean} animate - 是否显示动画
     */
    setTheme(theme, animate = true) {
        if (!this.isValidTheme(theme)) {
            console.warn(`无效的主题: ${theme}`);
            return;
        }

        const oldTheme = this.currentTheme;
        this.currentTheme = theme;

        if (animate && oldTheme !== theme) {
            this.animateThemeTransition(oldTheme, theme);
        } else {
            this.applyTheme(theme);
        }

        this.saveTheme(theme);
        this.updateThemeIcon();
        this.notifyThemeChange(theme, oldTheme);
    }

    /**
     * 应用主题
     * @param {string} theme - 主题名称
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新主题相关的CSS变量
        this.updateThemeVariables(theme);
        
        // 触发主题应用事件
        this.dispatchThemeEvent('themeApplied', { theme });
    }

    /**
     * 主题切换动画
     * @param {string} oldTheme - 旧主题
     * @param {string} newTheme - 新主题
     */
    animateThemeTransition(oldTheme, newTheme) {
        // 添加过渡类
        document.body.classList.add('theme-transitioning');
        
        // 创建过渡遮罩
        const overlay = this.createTransitionOverlay();
        document.body.appendChild(overlay);
        
        // 延迟应用新主题
        setTimeout(() => {
            this.applyTheme(newTheme);
        }, this.transitionDuration / 2);
        
        // 清理过渡效果
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, this.transitionDuration);
    }

    /**
     * 创建过渡遮罩
     * @returns {Element} 过渡遮罩元素
     */
    createTransitionOverlay() {
        const overlay = DOMHelper.createElement('div', {
            className: 'theme-transition-overlay',
            style: `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: var(--bg-primary);
                opacity: 0;
                z-index: 9999;
                pointer-events: none;
                transition: opacity ${this.transitionDuration / 2}ms ease-in-out;
            `
        });

        // 触发动画
        setTimeout(() => {
            overlay.style.opacity = '0.8';
        }, 10);

        setTimeout(() => {
            overlay.style.opacity = '0';
        }, this.transitionDuration / 2);

        return overlay;
    }

    /**
     * 更新主题变量
     * @param {string} theme - 主题名称
     */
    updateThemeVariables(theme) {
        const themeConfig = this.themes[theme];
        if (themeConfig && themeConfig.variables) {
            const root = document.documentElement;
            Object.entries(themeConfig.variables).forEach(([property, value]) => {
                root.style.setProperty(property, value);
            });
        }
    }

    /**
     * 更新主题图标
     */
    updateThemeIcon() {
        if (this.elements.themeIcon) {
            const icon = this.currentTheme === 'dark' ? '☀️' : '🌙';
            this.elements.themeIcon.textContent = icon;
            
            // 添加图标切换动画
            this.elements.themeIcon.style.transform = 'scale(0.8)';
            setTimeout(() => {
                this.elements.themeIcon.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * 保存主题设置
     * @param {string} theme - 主题名称
     */
    saveTheme(theme) {
        SettingsStorage.save(STORAGE_KEYS.theme, theme);
    }

    /**
     * 获取可用主题列表
     * @returns {Object} 主题配置对象
     */
    getAvailableThemes() {
        return {
            light: {
                name: '明亮模式',
                icon: '☀️',
                variables: {
                    '--bg-primary': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    '--bg-secondary': 'white',
                    '--text-primary': '#333',
                    '--text-secondary': '#666'
                }
            },
            dark: {
                name: '黑暗模式',
                icon: '🌙',
                variables: {
                    '--bg-primary': 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                    '--bg-secondary': '#2d2d44',
                    '--text-primary': '#e4e4e7',
                    '--text-secondary': '#a1a1aa'
                }
            }
        };
    }

    /**
     * 检查主题是否有效
     * @param {string} theme - 主题名称
     * @returns {boolean} 是否有效
     */
    isValidTheme(theme) {
        return theme && this.themes.hasOwnProperty(theme);
    }

    /**
     * 获取当前主题
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取主题信息
     * @param {string} theme - 主题名称
     * @returns {Object|null} 主题信息
     */
    getThemeInfo(theme) {
        return this.themes[theme] || null;
    }

    /**
     * 系统主题变化处理
     * @param {boolean} isDark - 是否为深色模式
     */
    onSystemThemeChange(isDark) {
        // 可以选择是否跟随系统主题
        const followSystem = SettingsStorage.load('followSystemTheme', false);
        if (followSystem) {
            const newTheme = isDark ? 'dark' : 'light';
            this.setTheme(newTheme, true);
            UIHelper.showMessage(`已跟随系统切换到${isDark ? '黑暗' : '明亮'}模式`);
        }
    }

    /**
     * 添加主题变化监听器
     * @param {Function} callback - 回调函数
     */
    addThemeChangeListener(callback) {
        if (typeof callback === 'function') {
            document.addEventListener('themeChanged', callback);
        }
    }

    /**
     * 移除主题变化监听器
     * @param {Function} callback - 回调函数
     */
    removeThemeChangeListener(callback) {
        if (typeof callback === 'function') {
            document.removeEventListener('themeChanged', callback);
        }
    }

    /**
     * 通知主题变化
     * @param {string} newTheme - 新主题
     * @param {string} oldTheme - 旧主题
     */
    notifyThemeChange(newTheme, oldTheme) {
        this.dispatchThemeEvent('themeChanged', {
            newTheme,
            oldTheme,
            timestamp: Date.now()
        });
    }

    /**
     * 分发主题事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchThemeEvent(eventType, detail) {
        const event = new CustomEvent(eventType, {
            detail,
            bubbles: true,
            cancelable: true
        });
        document.dispatchEvent(event);
    }

    /**
     * 预加载主题资源
     * @param {string} theme - 主题名称
     */
    preloadTheme(theme) {
        if (!this.isValidTheme(theme)) {
            return;
        }

        // 预加载主题相关的CSS或图片资源
        const themeConfig = this.themes[theme];
        if (themeConfig.preloadResources) {
            themeConfig.preloadResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.url;
                link.as = resource.type;
                document.head.appendChild(link);
            });
        }
    }

    /**
     * 获取主题统计信息
     * @returns {Object} 统计信息
     */
    getThemeStats() {
        return {
            currentTheme: this.currentTheme,
            availableThemes: Object.keys(this.themes),
            themeCount: Object.keys(this.themes).length,
            transitionDuration: this.transitionDuration
        };
    }

    /**
     * 重置主题为默认值
     */
    resetTheme() {
        this.setTheme(DEFAULT_SETTINGS.theme, true);
        UIHelper.showMessage('主题已重置为默认值');
    }

    /**
     * 导出主题配置
     * @returns {Object} 主题配置
     */
    exportThemeConfig() {
        return {
            currentTheme: this.currentTheme,
            themes: this.themes,
            settings: {
                followSystemTheme: SettingsStorage.load('followSystemTheme', false)
            }
        };
    }

    /**
     * 导入主题配置
     * @param {Object} config - 主题配置
     * @returns {boolean} 是否导入成功
     */
    importThemeConfig(config) {
        try {
            if (config.currentTheme && this.isValidTheme(config.currentTheme)) {
                this.setTheme(config.currentTheme, true);
            }
            
            if (config.themes) {
                this.themes = { ...this.themes, ...config.themes };
            }
            
            if (config.settings) {
                Object.entries(config.settings).forEach(([key, value]) => {
                    SettingsStorage.save(key, value);
                });
            }
            
            UIHelper.showMessage('主题配置导入成功');
            return true;
        } catch (error) {
            UIHelper.showMessage('主题配置导入失败：' + error.message, 'error');
            return false;
        }
    }

    /**
     * 销毁主题管理器
     */
    destroy() {
        // 清理事件监听器
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.removeEventListener('change', this.onSystemThemeChange);
        }
        
        // 清理过渡效果
        document.body.classList.remove('theme-transitioning');
        const overlay = document.querySelector('.theme-transition-overlay');
        if (overlay && overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    }
}
