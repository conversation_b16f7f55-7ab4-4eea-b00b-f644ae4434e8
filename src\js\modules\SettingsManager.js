/**
 * 设置管理模块
 * @fileoverview 负责应用设置的管理和配置
 */

import { WeatherApiClient } from '../utils/api.js';
import { SettingsStorage } from '../utils/storage.js';
import { DOMHelper, UIHelper } from '../utils/helpers.js';
import { API_CONFIG, DEFAULT_SETTINGS, STORAGE_KEYS } from '../constants/config.js';

/**
 * 设置管理器
 */
export class SettingsManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     * @param {Object} managers - 其他管理器实例
     */
    constructor(elements, managers = {}) {
        this.elements = elements;
        this.managers = managers;
        this.isModalOpen = false;
        this.settings = this.loadSettings();
        
        this.init();
    }

    /**
     * 初始化设置管理器
     */
    init() {
        this.bindEvents();
        this.loadSettingsToUI();
        this.updateApiHelp();
        this.updateApiStatus();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 设置按钮事件
        DOMHelper.addEventListener(this.elements.settingsToggle, 'click', () => {
            this.openSettings();
        });

        // 关闭模态框事件
        DOMHelper.addEventListener(this.elements.closeModal, 'click', () => {
            this.closeSettings();
        });

        // API选择变化事件
        DOMHelper.addEventListener(this.elements.weatherApiSelect, 'change', () => {
            this.updateApiHelp();
            this.updateApiStatus();
        });

        // 设置操作按钮事件
        DOMHelper.addEventListener(this.elements.testApiBtn, 'click', () => {
            this.testApiConnection();
        });

        DOMHelper.addEventListener(this.elements.saveSettingsBtn, 'click', () => {
            this.saveSettings();
        });

        DOMHelper.addEventListener(this.elements.resetSettingsBtn, 'click', () => {
            this.resetSettings();
        });

        // 点击模态框外部关闭
        DOMHelper.addEventListener(window, 'click', (e) => {
            if (e.target === this.elements.settingsModal) {
                this.closeSettings();
            }
        });

        // ESC键关闭模态框
        DOMHelper.addEventListener(document, 'keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen) {
                this.closeSettings();
            }
        });

        // API密钥输入变化事件
        DOMHelper.addEventListener(this.elements.apiKeyInput, 'input', () => {
            this.updateApiStatus();
        });

        // 刷新间隔变化事件
        DOMHelper.addEventListener(this.elements.refreshInterval, 'change', () => {
            this.onRefreshIntervalChange();
        });
    }

    /**
     * 打开设置页面
     */
    openSettings() {
        this.elements.settingsModal.style.display = 'block';
        this.elements.settingsModal.classList.add('show');
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        this.isModalOpen = true;
        
        // 重新加载设置到UI
        this.loadSettingsToUI();
    }

    /**
     * 关闭设置页面
     */
    closeSettings() {
        this.elements.settingsModal.classList.remove('show');
        setTimeout(() => {
            this.elements.settingsModal.style.display = 'none';
            document.body.style.overflow = ''; // 恢复背景滚动
        }, 300);
        this.isModalOpen = false;
    }

    /**
     * 加载设置到UI
     */
    loadSettingsToUI() {
        const settings = this.loadSettings();
        
        this.elements.apiKeyInput.value = settings.weatherApiKey || '';
        this.elements.notificationsEnabled.checked = settings.notificationsEnabled || false;
        this.elements.refreshInterval.value = settings.weatherRefreshInterval || DEFAULT_SETTINGS.refreshInterval;
        this.elements.weatherApiSelect.value = settings.weatherApiType || DEFAULT_SETTINGS.weatherApiType;
        
        this.updateApiHelp();
        this.updateApiStatus();
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const apiKey = this.elements.apiKeyInput.value.trim();
        const notificationsEnabled = this.elements.notificationsEnabled.checked;
        const refreshInterval = this.elements.refreshInterval.value;
        const apiType = this.elements.weatherApiSelect.value;

        // 验证API密钥格式
        if (apiKey && !WeatherApiClient.isValidApiKey(apiKey, apiType)) {
            UIHelper.showMessage('API密钥格式不正确，请检查后重试', 'error');
            return;
        }

        // 保存设置
        const settings = {
            weatherApiKey: apiKey,
            notificationsEnabled: notificationsEnabled,
            weatherRefreshInterval: refreshInterval,
            weatherApiType: apiType
        };

        Object.entries(settings).forEach(([key, value]) => {
            SettingsStorage.save(STORAGE_KEYS[key], value);
        });

        this.settings = { ...this.settings, ...settings };

        // 请求通知权限
        if (notificationsEnabled && Notification.permission === 'default') {
            this.requestNotificationPermission();
        }

        // 更新天气刷新间隔
        if (this.managers.weatherManager) {
            this.managers.weatherManager.updateRefreshInterval(refreshInterval);
        }

        UIHelper.showMessage('设置保存成功');
        this.updateApiStatus();
    }

    /**
     * 重置设置
     */
    resetSettings() {
        if (confirm('确定要重置所有设置为默认值吗？')) {
            // 重置为默认设置
            SettingsStorage.reset();
            this.settings = this.loadSettings();
            
            // 更新UI
            this.loadSettingsToUI();
            
            // 重置天气刷新间隔
            if (this.managers.weatherManager) {
                this.managers.weatherManager.updateRefreshInterval(DEFAULT_SETTINGS.refreshInterval);
            }
            
            UIHelper.showMessage('设置已重置为默认值');
        }
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        const apiKey = this.elements.apiKeyInput.value.trim();
        const apiType = this.elements.weatherApiSelect.value;
        
        if (!apiKey) {
            UIHelper.showMessage('请先输入API密钥', 'warning');
            return;
        }

        // 根据API类型验证密钥格式
        if (!WeatherApiClient.isValidApiKey(apiKey, apiType)) {
            UIHelper.showMessage('API密钥格式不正确，请检查后重试', 'error');
            return;
        }

        // 显示测试中状态
        this.elements.testApiBtn.disabled = true;
        this.elements.testApiBtn.textContent = '测试中...';
        this.elements.apiStatus.innerHTML = '<div class="api-status testing">正在测试连接...</div>';

        try {
            // 测试API连接
            await WeatherApiClient.testConnection(apiKey, apiType);
            
            // 测试成功
            this.elements.apiStatus.innerHTML = '<div class="api-status success">✅ API连接成功</div>';
            UIHelper.showMessage('API连接测试成功', 'success');
            
            // 保存测试成功状态
            SettingsStorage.save(STORAGE_KEYS.apiTestSuccess, true);
            SettingsStorage.save(STORAGE_KEYS.apiTestTime, Date.now());
            
        } catch (error) {
            console.error('API测试失败:', error);
            
            let errorMessage = 'API连接失败';
            if (error.message.includes('401')) {
                errorMessage = 'API密钥无效';
            } else if (error.message.includes('404')) {
                errorMessage = 'API服务不可用';
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接失败';
            } else if (error.message.includes('超时')) {
                errorMessage = '连接超时';
            }
            
            this.elements.apiStatus.innerHTML = `<div class="api-status error">❌ ${errorMessage}</div>`;
            UIHelper.showMessage(`API测试失败: ${errorMessage}`, 'error');
            
        } finally {
            // 恢复按钮状态
            this.elements.testApiBtn.disabled = false;
            this.elements.testApiBtn.textContent = '测试连接';
        }
    }

    /**
     * 更新API帮助信息
     */
    updateApiHelp() {
        const selectedApi = this.elements.weatherApiSelect.value;
        const helpInfo = WeatherApiClient.getApiHelp(selectedApi);
        
        this.elements.apiHelpText.innerHTML = `
            获取免费API密钥的步骤：<br>
            ${helpInfo.steps.map((step, index) => `${index + 1}. ${step}`).join('<br>')}
            <br><br>
            <a href="${helpInfo.url}" target="_blank">访问 ${helpInfo.title} 官网</a>
        `;
    }

    /**
     * 更新API状态显示
     */
    updateApiStatus() {
        const apiKey = this.elements.apiKeyInput.value.trim();
        const apiType = this.elements.weatherApiSelect.value;
        
        if (!apiKey) {
            this.elements.apiStatus.innerHTML = '<div class="api-status none">❌ 未配置API密钥</div>';
            return;
        }

        if (!WeatherApiClient.isValidApiKey(apiKey, apiType)) {
            this.elements.apiStatus.innerHTML = '<div class="api-status error">❌ API密钥格式错误</div>';
            return;
        }

        // 检查是否有之前成功的测试记录
        const lastTestSuccess = SettingsStorage.load(STORAGE_KEYS.apiTestSuccess, false);
        const lastTestTime = SettingsStorage.load(STORAGE_KEYS.apiTestTime, null);
        
        if (lastTestSuccess && lastTestTime) {
            const testTime = new Date(parseInt(lastTestTime));
            const now = new Date();
            const hoursDiff = (now - testTime) / (1000 * 60 * 60);
            
            if (hoursDiff < 24) { // 24小时内的测试结果
                this.elements.apiStatus.innerHTML = '<div class="api-status success">✅ API密钥有效（最近测试成功）</div>';
                return;
            }
        }

        this.elements.apiStatus.innerHTML = '<div class="api-status unknown">⚠️ API密钥已配置但未测试</div>';
    }

    /**
     * 刷新间隔变化处理
     */
    onRefreshIntervalChange() {
        const interval = this.elements.refreshInterval.value;
        
        // 实时更新天气刷新间隔（如果设置已保存）
        if (this.managers.weatherManager) {
            this.managers.weatherManager.updateRefreshInterval(interval);
        }
    }

    /**
     * 请求通知权限
     */
    async requestNotificationPermission() {
        try {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    UIHelper.showMessage('通知权限已授予');
                } else {
                    UIHelper.showMessage('通知权限被拒绝，无法显示提醒通知', 'warning');
                }
            } else {
                UIHelper.showMessage('当前浏览器不支持通知功能', 'warning');
            }
        } catch (error) {
            console.error('请求通知权限失败:', error);
            UIHelper.showMessage('请求通知权限失败', 'error');
        }
    }

    /**
     * 加载所有设置
     * @returns {Object} 设置对象
     */
    loadSettings() {
        return SettingsStorage.loadAll();
    }

    /**
     * 获取特定设置
     * @param {string} key - 设置键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 设置值
     */
    getSetting(key, defaultValue = null) {
        return this.settings[key] !== undefined ? this.settings[key] : defaultValue;
    }

    /**
     * 设置特定值
     * @param {string} key - 设置键名
     * @param {any} value - 设置值
     * @returns {boolean} 是否设置成功
     */
    setSetting(key, value) {
        this.settings[key] = value;
        return SettingsStorage.save(STORAGE_KEYS[key], value);
    }

    /**
     * 导出设置
     * @returns {Object} 设置数据
     */
    exportSettings() {
        return {
            settings: this.settings,
            timestamp: Date.now(),
            version: '1.0.0'
        };
    }

    /**
     * 导入设置
     * @param {Object} data - 设置数据
     * @returns {boolean} 是否导入成功
     */
    importSettings(data) {
        try {
            if (data.settings && typeof data.settings === 'object') {
                // 验证设置数据
                const validSettings = {};
                Object.entries(data.settings).forEach(([key, value]) => {
                    if (STORAGE_KEYS[key]) {
                        validSettings[key] = value;
                    }
                });

                // 保存设置
                SettingsStorage.saveAll(validSettings);
                this.settings = { ...this.settings, ...validSettings };
                
                // 更新UI
                this.loadSettingsToUI();
                
                UIHelper.showMessage('设置导入成功');
                return true;
            }
            throw new Error('设置数据格式不正确');
        } catch (error) {
            UIHelper.showMessage('设置导入失败：' + error.message, 'error');
            return false;
        }
    }

    /**
     * 获取设置统计信息
     * @returns {Object} 统计信息
     */
    getSettingsStats() {
        return {
            totalSettings: Object.keys(this.settings).length,
            configuredSettings: Object.values(this.settings).filter(value => 
                value !== null && value !== undefined && value !== ''
            ).length,
            apiConfigured: !!this.settings.weatherApiKey,
            notificationsEnabled: this.settings.notificationsEnabled,
            lastModified: SettingsStorage.load('settingsLastModified', null)
        };
    }

    /**
     * 验证设置完整性
     * @returns {Object} 验证结果
     */
    validateSettings() {
        const issues = [];
        const warnings = [];

        // 检查API密钥
        if (!this.settings.weatherApiKey) {
            warnings.push('未配置天气API密钥，将使用模拟数据');
        } else if (!WeatherApiClient.isValidApiKey(this.settings.weatherApiKey, this.settings.weatherApiType)) {
            issues.push('API密钥格式不正确');
        }

        // 检查通知权限
        if (this.settings.notificationsEnabled && Notification.permission !== 'granted') {
            warnings.push('已启用通知但未授予权限');
        }

        // 检查刷新间隔
        const interval = parseInt(this.settings.weatherRefreshInterval);
        if (isNaN(interval) || interval < 1) {
            issues.push('天气刷新间隔设置无效');
        }

        return {
            isValid: issues.length === 0,
            issues,
            warnings,
            score: Math.max(0, 100 - (issues.length * 20) - (warnings.length * 10))
        };
    }

    /**
     * 销毁设置管理器
     */
    destroy() {
        this.closeSettings();
        this.settings = null;
        this.managers = null;
    }
}
