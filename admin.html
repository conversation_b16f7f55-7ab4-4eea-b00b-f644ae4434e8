<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BigBuleBook - 管理员后台</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-styles.css">
</head>
<body class="admin-page">
    <!-- 登录容器 -->
    <div id="loginContainer" class="login-container">
        <div class="login-form">
            <div class="login-header">
                <h1>🔐 管理员登录</h1>
                <p>BigBuleBook 管理员后台</p>
            </div>
            
            <div class="login-body">
                <div class="input-group">
                    <label for="adminPassword">管理员密码</label>
                    <input type="password" id="adminPassword" placeholder="请输入管理员密码" autocomplete="current-password">
                </div>
                
                <div id="loginError" class="error-message" style="display: none;"></div>
                
                <button id="loginBtn" class="login-btn" onclick="console.log('登录按钮被点击')">登录</button>
                
                <div class="login-footer">
                    <p class="privacy-notice">
                        <small>⚠️ 此系统仅供授权管理员使用。所有操作将被记录。</small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 仪表板容器 -->
    <div id="dashboardContainer" class="dashboard-container" style="display: none;">
        <!-- 顶部导航 -->
        <header class="admin-header">
            <div class="header-left">
                <h1>📊 BigBuleBook 管理后台</h1>
                <span class="version">v2.0.0</span>
            </div>
            <div class="header-right">
                <button id="refreshBtn" class="btn btn-secondary">🔄 刷新数据</button>
                <button id="logoutBtn" class="btn btn-danger">🚪 退出</button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="admin-main">
            <!-- 标签页导航 -->
            <nav class="tab-nav">
                <button class="tab-btn active" data-tab="overview">📈 概览</button>
                <button class="tab-btn" data-tab="users">👥 用户管理</button>
                <button class="tab-btn" data-tab="analytics">📊 数据分析</button>
                <button class="tab-btn" data-tab="system">⚙️ 系统信息</button>
            </nav>

            <!-- 概览标签页 -->
            <div id="overviewTab" class="tab-content">
                <div class="overview-grid">
                    <!-- 统计卡片 -->
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalUsers">0</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <div class="stat-value" id="activeUsers7d">0</div>
                            <div class="stat-label">7天活跃用户</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <div class="stat-value" id="todayActiveUsers">0</div>
                            <div class="stat-label">今日活跃用户</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalEvents">0</div>
                            <div class="stat-label">总事件数</div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <h3>📈 用户活跃度趋势</h3>
                        <div id="activityChart" class="chart-container"></div>
                    </div>
                    
                    <div class="chart-card">
                        <h3>📊 事件类型分布</h3>
                        <div id="eventChart" class="chart-container"></div>
                    </div>
                </div>
            </div>

            <!-- 用户管理标签页 -->
            <div id="usersTab" class="tab-content" style="display: none;">
                <div class="users-header">
                    <h2>👥 用户管理</h2>
                    <div class="users-actions">
                        <button id="exportJsonBtn" class="btn btn-primary">📄 导出JSON</button>
                        <button id="exportCsvBtn" class="btn btn-primary">📊 导出CSV</button>
                    </div>
                </div>
                
                <div class="users-table">
                    <div class="table-header">
                        <div class="table-cell">用户ID</div>
                        <div class="table-cell">IP地址</div>
                        <div class="table-cell">事件数</div>
                        <div class="table-cell">会话数</div>
                        <div class="table-cell">最后访问</div>
                        <div class="table-cell">操作</div>
                    </div>
                    <div id="userList" class="table-body">
                        <!-- 用户列表将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 数据分析标签页 -->
            <div id="analyticsTab" class="tab-content" style="display: none;">
                <div class="analytics-header">
                    <h2>📊 数据分析</h2>
                    <div class="analytics-actions">
                        <button id="cleanupBtn" class="btn btn-warning">🧹 清理旧数据</button>
                    </div>
                </div>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>📈 详细统计</h3>
                        <div class="stats-list">
                            <div class="stat-item">
                                <span class="stat-name">总会话数:</span>
                                <span class="stat-value" id="totalSessions">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-name">平均每用户事件数:</span>
                                <span class="stat-value" id="avgEventsPerUser">0</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>🔒 隐私设置</h3>
                        <div class="privacy-info">
                            <p>✅ 数据已匿名化处理</p>
                            <p>✅ 敏感信息已脱敏</p>
                            <p>✅ 符合隐私保护要求</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息标签页 -->
            <div id="systemTab" class="tab-content" style="display: none;">
                <div class="system-header">
                    <h2>⚙️ 系统信息</h2>
                </div>
                
                <div class="system-grid">
                    <div class="system-card">
                        <h3>📋 基本信息</h3>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">系统版本:</span>
                                <span class="info-value" id="systemVersion">2.0.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">最后更新:</span>
                                <span class="info-value" id="lastUpdate">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">会话过期时间:</span>
                                <span class="info-value" id="sessionExpiry">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="system-card">
                        <h3>🔐 安全状态</h3>
                        <div class="security-status">
                            <div class="status-item">
                                <span class="status-indicator success"></span>
                                <span>管理员已认证</span>
                            </div>
                            <div class="status-item">
                                <span class="status-indicator success"></span>
                                <span>数据加密正常</span>
                            </div>
                            <div class="status-item">
                                <span class="status-indicator success"></span>
                                <span>访问日志记录中</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="system-card">
                        <h3>📜 隐私政策</h3>
                        <div class="privacy-policy">
                            <p><strong>数据收集说明：</strong></p>
                            <ul>
                                <li>仅收集必要的使用统计数据</li>
                                <li>所有个人敏感信息已匿名化</li>
                                <li>数据仅用于改善用户体验</li>
                                <li>用户可随时选择退出数据收集</li>
                                <li>数据保留期限不超过30天</li>
                            </ul>
                            <p><strong>安全保障：</strong></p>
                            <ul>
                                <li>采用客户端加密技术</li>
                                <li>严格的访问权限控制</li>
                                <li>完整的操作审计日志</li>
                                <li>定期清理过期数据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 脚本 -->
    <script type="module" src="src/js/admin.js"></script>
</body>
</html>
