/**
 * 主应用类
 * @fileoverview 应用的主控制器，协调各个功能模块
 */

import { TodoManager } from './modules/TodoManager.js';
import { ClockManager } from './modules/ClockManager.js';
import { WeatherManager } from './modules/WeatherManager.js';
import { ThemeManager } from './modules/ThemeManager.js';
import { SettingsManager } from './modules/SettingsManager.js';
import { FolderManager } from './modules/FolderManager.js';
import { DOMHelper, UIHelper } from './utils/helpers.js';

/**
 * 主应用类
 */
export class TodoApp {
    /**
     * 构造函数
     */
    constructor() {
        this.elements = {};
        this.managers = {};
        this.intervals = {};
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        try {
            this.initElements();
            this.initManagers();
            this.bindGlobalEvents();
            this.startIntervals();
            this.isInitialized = true;
            
            console.log('TodoApp 初始化完成');
        } catch (error) {
            console.error('TodoApp 初始化失败:', error);
            UIHelper.showMessage('应用初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 初始化DOM元素
     */
    initElements() {
        // 基本元素
        this.elements.todoInput = DOMHelper.getElement('#todoInput');
        this.elements.addBtn = DOMHelper.getElement('#addBtn');
        this.elements.todoList = DOMHelper.getElement('#todoList');
        this.elements.filterBtns = DOMHelper.getElements('.filter-btn');
        this.elements.totalCount = DOMHelper.getElement('#totalCount');
        this.elements.activeCount = DOMHelper.getElement('#activeCount');
        this.elements.completedCount = DOMHelper.getElement('#completedCount');
        this.elements.overdueCount = DOMHelper.getElement('#overdueCount');

        // 待办事项相关元素
        this.elements.prioritySelect = DOMHelper.getElement('#prioritySelect');
        this.elements.categorySelect = DOMHelper.getElement('#categorySelect');
        this.elements.reminderInput = DOMHelper.getElement('#reminderInput');
        this.elements.categoryBtns = DOMHelper.getElements('.category-btn');
        this.elements.priorityBtns = DOMHelper.getElements('.priority-btn');
        this.elements.remindersList = DOMHelper.getElement('#remindersList');

        // 文件夹相关元素
        this.elements.folderSelect = DOMHelper.getElement('#folderSelect');
        this.elements.addFolderBtn = DOMHelper.getElement('#addFolderBtn');

        // 时钟相关元素
        this.elements.digitalTime = DOMHelper.getElement('#digitalTime');

        // 天气相关元素
        this.elements.countrySelect = DOMHelper.getElement('#countrySelect');
        this.elements.regionSelect = DOMHelper.getElement('#regionSelect');
        this.elements.refreshWeather = DOMHelper.getElement('#refreshWeather');
        this.elements.weatherDisplay = DOMHelper.getElement('#weatherDisplay');

        // 主题切换元素
        this.elements.themeToggle = DOMHelper.getElement('#themeToggle');
        this.elements.themeIcon = DOMHelper.getElement('.theme-icon');

        // 设置相关元素
        this.elements.settingsToggle = DOMHelper.getElement('#settingsToggle');
        this.elements.settingsModal = DOMHelper.getElement('#settingsModal');
        this.elements.closeModal = DOMHelper.getElement('.close');
        this.elements.weatherApiSelect = DOMHelper.getElement('#weatherApiSelect');
        this.elements.apiKeyInput = DOMHelper.getElement('#apiKeyInput');
        this.elements.apiStatus = DOMHelper.getElement('#apiStatus');
        this.elements.testApiBtn = DOMHelper.getElement('#testApiBtn');
        this.elements.saveSettingsBtn = DOMHelper.getElement('#saveSettingsBtn');
        this.elements.resetSettingsBtn = DOMHelper.getElement('#resetSettingsBtn');
        this.elements.notificationsEnabled = DOMHelper.getElement('#notificationsEnabled');
        this.elements.refreshInterval = DOMHelper.getElement('#refreshInterval');
        this.elements.apiHelpText = DOMHelper.getElement('#apiHelpText');

        // 搜索相关元素
        this.elements.searchInput = DOMHelper.getElement('#searchInput');
        this.elements.clearSearchBtn = DOMHelper.getElement('#clearSearchBtn');

        // 自定义分类相关元素
        this.elements.customCategoryInput = DOMHelper.getElement('#customCategoryInput');

        // 验证关键元素是否存在
        this.validateElements();
    }

    /**
     * 验证关键元素是否存在
     */
    validateElements() {
        const requiredElements = [
            'todoInput', 'addBtn', 'todoList', 'digitalTime',
            'weatherDisplay', 'themeToggle', 'settingsModal'
        ];

        const missingElements = requiredElements.filter(key => !this.elements[key]);
        
        if (missingElements.length > 0) {
            throw new Error(`缺少关键DOM元素: ${missingElements.join(', ')}`);
        }
    }

    /**
     * 初始化各个管理器
     */
    initManagers() {
        // 初始化文件夹管理器
        this.managers.folderManager = new FolderManager(
            this.elements,
            (folderId) => this.onFolderChange(folderId)
        );

        // 初始化待办事项管理器
        this.managers.todoManager = new TodoManager(this.elements);

        // 初始化时钟管理器
        this.managers.clockManager = new ClockManager(this.elements);

        // 初始化天气管理器
        this.managers.weatherManager = new WeatherManager(this.elements);

        // 初始化主题管理器
        this.managers.themeManager = new ThemeManager(this.elements);

        // 初始化设置管理器
        this.managers.settingsManager = new SettingsManager(this.elements, this.managers);

        // 设置管理器间的关联
        this.setupManagerRelations();

        // 设置用户跟踪器到其他管理器
        this.setupUserTracking();
    }

    /**
     * 设置管理器间的关联关系
     */
    setupManagerRelations() {
        // 监听文件夹删除事件，移动待办事项到默认文件夹
        document.addEventListener('folderDeleted', (e) => {
            this.handleFolderDeleted(e.detail.folderId);
        });

        // 监听主题变化事件，更新时钟主题
        document.addEventListener('themeChanged', (e) => {
            this.managers.clockManager.setTheme(e.detail.newTheme);

            // 跟踪主题变化事件
            if (this.managers.userTracker) {
                this.managers.userTracker.trackEvent(TRACKING_CONFIG.EVENTS.THEME_CHANGE, {
                    newTheme: e.detail.newTheme,
                    oldTheme: e.detail.oldTheme
                });
            }
        });
    }

    /**
     * 设置用户跟踪
     */
    setupUserTracking() {
        // 为待办事项管理器设置用户跟踪器
        if (this.managers.todoManager && this.managers.userTracker) {
            this.managers.todoManager.setUserTracker(this.managers.userTracker);
        }

        // 跟踪文件夹创建事件
        document.addEventListener('folderChanged', (e) => {
            if (this.managers.userTracker) {
                this.managers.userTracker.trackEvent(TRACKING_CONFIG.EVENTS.FOLDER_CREATE, {
                    folderId: e.detail.folderId,
                    folderName: e.detail.folderName
                });
            }
        });
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 页面可见性变化事件
        DOMHelper.addEventListener(document, 'visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 窗口大小变化事件
        DOMHelper.addEventListener(window, 'resize', UIHelper.debounce(() => {
            this.handleWindowResize();
        }, 250));

        // 页面卸载事件
        DOMHelper.addEventListener(window, 'beforeunload', () => {
            this.cleanup();
        });

        // 键盘快捷键
        DOMHelper.addEventListener(document, 'keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * 启动定时器
     */
    startIntervals() {
        // 提醒检查定时器（每分钟检查一次）
        this.intervals.reminderCheck = setInterval(() => {
            this.managers.todoManager.checkReminders();
        }, 60000);

        // 应用状态保存定时器（每5分钟保存一次）
        this.intervals.autoSave = setInterval(() => {
            this.autoSave();
        }, 300000);
    }

    /**
     * 文件夹变化处理
     * @param {string} folderId - 文件夹ID
     */
    onFolderChange(folderId) {
        this.managers.todoManager.setCurrentFolder(folderId);
    }

    /**
     * 处理文件夹删除
     * @param {string} deletedFolderId - 被删除的文件夹ID
     */
    handleFolderDeleted(deletedFolderId) {
        // 将被删除文件夹中的待办事项移动到默认文件夹
        const todos = this.managers.todoManager.getAllTodos();
        let movedCount = 0;

        todos.forEach(todo => {
            if (todo.folder === deletedFolderId) {
                todo.folder = 'default';
                movedCount++;
            }
        });

        if (movedCount > 0) {
            this.managers.todoManager.saveTodos();
            this.managers.todoManager.render();
            UIHelper.showMessage(`已将 ${movedCount} 个待办事项移动到默认文件夹`);
        }
    }

    /**
     * 页面可见性变化处理
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停一些功能
            this.managers.clockManager.stop();
            this.managers.weatherManager.stopAutoRefresh();
        } else {
            // 页面显示时恢复功能
            this.managers.clockManager.start();
            this.managers.weatherManager.startAutoRefresh();
            // 刷新数据
            this.managers.todoManager.render();
            this.managers.weatherManager.updateWeather();
        }
    }

    /**
     * 窗口大小变化处理
     */
    handleWindowResize() {
        // 可以在这里处理响应式布局调整
        console.log('窗口大小已变化');
    }

    /**
     * 键盘快捷键处理
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: 新建待办事项
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.elements.todoInput.focus();
        }

        // Ctrl/Cmd + T: 切换主题
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            this.managers.themeManager.toggleTheme();
        }

        // Ctrl/Cmd + ,: 打开设置
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.managers.settingsManager.openSettings();
        }

        // F5: 刷新天气
        if (e.key === 'F5' && !e.ctrlKey && !e.metaKey) {
            e.preventDefault();
            this.managers.weatherManager.updateWeather(true);
        }
    }

    /**
     * 自动保存
     */
    autoSave() {
        try {
            // 这里可以添加自动保存逻辑
            console.log('自动保存完成');
        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态信息
     */
    getAppStatus() {
        return {
            isInitialized: this.isInitialized,
            managers: {
                todo: this.managers.todoManager ? this.managers.todoManager.getStats() : null,
                clock: this.managers.clockManager ? this.managers.clockManager.getStatus() : null,
                weather: this.managers.weatherManager ? this.managers.weatherManager.getStatus() : null,
                theme: this.managers.themeManager ? this.managers.themeManager.getCurrentTheme() : null,
                folder: this.managers.folderManager ? this.managers.folderManager.getFolderStats() : null
            },
            intervals: Object.keys(this.intervals).map(key => ({
                name: key,
                active: this.intervals[key] !== null
            }))
        };
    }

    /**
     * 导出应用数据
     * @returns {Object} 应用数据
     */
    exportAppData() {
        return {
            todos: this.managers.todoManager.exportData('json'),
            folders: this.managers.folderManager.exportFolders(),
            settings: this.managers.settingsManager.exportSettings(),
            theme: this.managers.themeManager.exportThemeConfig(),
            timestamp: Date.now(),
            version: '1.0.0'
        };
    }

    /**
     * 导入应用数据
     * @param {Object} data - 应用数据
     * @returns {boolean} 是否导入成功
     */
    importAppData(data) {
        try {
            let success = true;

            if (data.todos) {
                success = success && this.managers.todoManager.importData(data.todos);
            }

            if (data.folders) {
                success = success && this.managers.folderManager.importFolders(data.folders);
            }

            if (data.settings) {
                success = success && this.managers.settingsManager.importSettings(data.settings);
            }

            if (data.theme) {
                success = success && this.managers.themeManager.importThemeConfig(data.theme);
            }

            if (success) {
                UIHelper.showMessage('应用数据导入成功');
            }

            return success;
        } catch (error) {
            UIHelper.showMessage('应用数据导入失败：' + error.message, 'error');
            return false;
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理定时器
        Object.values(this.intervals).forEach(interval => {
            if (interval) {
                clearInterval(interval);
            }
        });

        // 销毁管理器
        Object.values(this.managers).forEach(manager => {
            if (manager && typeof manager.destroy === 'function') {
                manager.destroy();
            }
        });

        console.log('TodoApp 资源清理完成');
    }

    /**
     * 重启应用
     */
    restart() {
        this.cleanup();
        setTimeout(() => {
            this.init();
        }, 100);
    }

    /**
     * 获取管理器实例
     * @param {string} name - 管理器名称
     * @returns {Object|null} 管理器实例
     */
    getManager(name) {
        return this.managers[name] || null;
    }
}
