/**
 * API调用工具类
 * @fileoverview 提供天气API调用和数据处理功能
 */

import { API_CONFIG, WEATHER_ICONS } from '../constants/config.js';

/**
 * API工具类
 */
export class ApiClient {
    /**
     * 发送HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        };

        const config = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, config);
            return response;
        } catch (error) {
            throw new Error(`网络请求失败: ${error.message}`);
        }
    }

    /**
     * 发送GET请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    static async get(url, options = {}) {
        const response = await this.request(url, { ...options, method: 'GET' });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 验证API密钥格式
     * @param {string} apiKey - API密钥
     * @param {string} apiType - API类型
     * @returns {boolean} 是否有效
     */
    static isValidApiKey(apiKey, apiType = 'openweathermap') {
        if (!apiKey || typeof apiKey !== 'string') {
            return false;
        }

        switch (apiType) {
            case 'openweathermap':
                return /^[a-f0-9]{32}$/.test(apiKey);
            case 'weatherapi':
                return /^[a-f0-9]{32}$/.test(apiKey);
            case 'weathermap':
                return /^[a-f0-9]{32}$/.test(apiKey);
            default:
                return apiKey.length >= 16;
        }
    }
}

/**
 * 天气API客户端
 */
export class WeatherApiClient extends ApiClient {
    /**
     * 获取天气数据
     * @param {string} cityName - 城市名称
     * @param {string} apiKey - API密钥
     * @param {string} apiType - API类型
     * @returns {Promise<Object>} 天气数据
     */
    static async getWeatherData(cityName, apiKey, apiType = 'openweathermap') {
        if (!apiKey) {
            throw new Error('API密钥不能为空');
        }

        if (!this.isValidApiKey(apiKey, apiType)) {
            throw new Error('API密钥格式不正确');
        }

        try {
            switch (apiType) {
                case 'openweathermap':
                    return await this.fetchOpenWeatherMapData(cityName, apiKey);
                case 'weatherapi':
                    return await this.fetchWeatherApiData(cityName, apiKey);
                case 'weathermap':
                    return await this.fetchWeatherMapData(cityName, apiKey);
                default:
                    throw new Error('不支持的API类型');
            }
        } catch (error) {
            if (error.message.includes('401')) {
                const apiNames = {
                    'openweathermap': 'OpenWeatherMap',
                    'weatherapi': 'WeatherAPI',
                    'weathermap': 'WeatherMap'
                };
                throw new Error(`${apiNames[apiType]} API密钥无效，请检查密钥是否正确`);
            }
            throw error;
        }
    }

    /**
     * 获取OpenWeatherMap天气数据
     * @param {string} cityName - 城市名称
     * @param {string} apiKey - API密钥
     * @returns {Promise<Object>} 天气数据
     */
    static async fetchOpenWeatherMapData(cityName, apiKey) {
        const apiUrl = `${API_CONFIG.WEATHER_APIS.openweathermap.baseUrl}?q=${cityName}&appid=${apiKey}&units=metric&lang=zh_cn`;
        
        const data = await this.get(apiUrl);
        
        if (data.cod !== 200) {
            throw new Error(data.message || 'API返回错误');
        }

        return {
            city: data.name,
            temperature: Math.round(data.main.temp),
            description: data.weather[0].description,
            humidity: data.main.humidity,
            windSpeed: data.wind.speed,
            pressure: data.main.pressure,
            icon: this.getWeatherIcon(data.weather[0].icon, 'openweathermap'),
            feelsLike: Math.round(data.main.feels_like),
            uvIndex: data.uvi || 0
        };
    }

    /**
     * 获取WeatherAPI天气数据
     * @param {string} cityName - 城市名称
     * @param {string} apiKey - API密钥
     * @returns {Promise<Object>} 天气数据
     */
    static async fetchWeatherApiData(cityName, apiKey) {
        const apiUrl = `${API_CONFIG.WEATHER_APIS.weatherapi.baseUrl}?key=${apiKey}&q=${cityName}&lang=zh`;
        
        const data = await this.get(apiUrl);
        
        if (data.error) {
            throw new Error(data.error.message || 'API返回错误');
        }

        return {
            city: data.location.name,
            temperature: Math.round(data.current.temp_c),
            description: data.current.condition.text,
            humidity: data.current.humidity,
            windSpeed: data.current.wind_kph / 3.6, // 转换为m/s
            pressure: data.current.pressure_mb,
            icon: this.getWeatherIcon(data.current.condition.code.toString(), 'weatherapi'),
            feelsLike: Math.round(data.current.feelslike_c),
            uvIndex: data.current.uv || 0
        };
    }

    /**
     * 获取WeatherMap天气数据
     * @param {string} cityName - 城市名称
     * @param {string} apiKey - API密钥
     * @returns {Promise<Object>} 天气数据
     */
    static async fetchWeatherMapData(cityName, apiKey) {
        const apiUrl = `${API_CONFIG.WEATHER_APIS.weathermap.baseUrl}?q=${cityName}&appid=${apiKey}&units=metric&lang=zh_cn`;
        
        const data = await this.get(apiUrl);
        
        if (data.cod !== 200) {
            throw new Error(data.message || 'API返回错误');
        }

        return {
            city: data.name,
            temperature: Math.round(data.main.temp),
            description: data.weather[0].description,
            humidity: data.main.humidity,
            windSpeed: data.wind.speed,
            pressure: data.main.pressure,
            icon: this.getWeatherIcon(data.weather[0].icon, 'weathermap'),
            feelsLike: Math.round(data.main.feels_like),
            uvIndex: data.uvi || 0
        };
    }

    /**
     * 获取天气图标
     * @param {string} iconCode - 图标代码
     * @param {string} apiType - API类型
     * @returns {string} 天气图标
     */
    static getWeatherIcon(iconCode, apiType) {
        const iconMap = WEATHER_ICONS[apiType];
        return iconMap[iconCode] || '🌤️';
    }

    /**
     * 测试API连接
     * @param {string} apiKey - API密钥
     * @param {string} apiType - API类型
     * @returns {Promise<boolean>} 是否连接成功
     */
    static async testConnection(apiKey, apiType = 'openweathermap') {
        try {
            await this.getWeatherData('Beijing', apiKey, apiType);
            return true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取API帮助信息
     * @param {string} apiType - API类型
     * @returns {Object} API帮助信息
     */
    static getApiHelp(apiType) {
        const helpTexts = {
            openweathermap: {
                title: 'OpenWeatherMap API',
                steps: [
                    '访问 OpenWeatherMap API 官网',
                    '注册免费账户',
                    '在API密钥页面获取您的密钥',
                    '将密钥粘贴到输入框中',
                    '点击保存按钮'
                ],
                url: API_CONFIG.WEATHER_APIS.openweathermap.helpUrl
            },
            weatherapi: {
                title: 'WeatherAPI',
                steps: [
                    '访问 WeatherAPI 官网',
                    '注册免费账户',
                    '在仪表板中获取API密钥',
                    '将密钥粘贴到输入框中',
                    '点击保存按钮'
                ],
                url: API_CONFIG.WEATHER_APIS.weatherapi.helpUrl
            },
            weathermap: {
                title: 'WeatherMap API',
                steps: [
                    '访问 WeatherMap API 官网',
                    '注册免费账户',
                    '在API密钥页面获取您的密钥',
                    '将密钥粘贴到输入框中',
                    '点击保存按钮'
                ],
                url: API_CONFIG.WEATHER_APIS.weathermap.helpUrl
            }
        };

        return helpTexts[apiType] || helpTexts.openweathermap;
    }
}
