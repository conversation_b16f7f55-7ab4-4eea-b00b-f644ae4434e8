/**
 * 时钟管理模块
 * @fileoverview 负责模拟时钟和数字时钟的显示和更新
 */

import { <PERSON><PERSON><PERSON>elper, DateHelper } from '../utils/helpers.js';

/**
 * 时钟管理器
 */
export class ClockManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     */
    constructor(elements) {
        this.elements = elements;
        this.clockInterval = null;
        this.isRunning = false;
        
        this.init();
    }

    /**
     * 初始化时钟
     */
    init() {
        this.createClockMarks();
        this.start();
    }

    /**
     * 创建时钟刻度
     */
    createClockMarks() {
        const clockFace = DOMHelper.getElement('.clock-face');
        if (!clockFace) return;

        // 创建小时刻度
        for (let i = 1; i <= 12; i++) {
            const mark = DOMHelper.createElement('div', {
                className: 'hour-mark',
                style: `
                    position: absolute;
                    width: 2px;
                    height: 15px;
                    background: var(--text-primary);
                    top: 5px;
                    left: 50%;
                    transform-origin: 1px 95px;
                    transform: translateX(-50%) rotate(${i * 30}deg);
                `
            });
            clockFace.appendChild(mark);
        }

        // 创建分钟刻度
        for (let i = 1; i <= 60; i++) {
            if (i % 5 !== 0) { // 跳过小时刻度位置
                const mark = DOMHelper.createElement('div', {
                    className: 'minute-mark',
                    style: `
                        position: absolute;
                        width: 1px;
                        height: 8px;
                        background: var(--text-secondary);
                        top: 5px;
                        left: 50%;
                        transform-origin: 0.5px 95px;
                        transform: translateX(-50%) rotate(${i * 6}deg);
                    `
                });
                clockFace.appendChild(mark);
            }
        }

        // 添加数字
        for (let i = 1; i <= 12; i++) {
            const number = DOMHelper.createElement('div', {
                className: 'clock-number',
                style: `
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    font-size: 12px;
                    font-weight: bold;
                    color: var(--text-primary);
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) translate(${Math.sin(i * Math.PI / 6) * 75}px, ${-Math.cos(i * Math.PI / 6) * 75}px);
                `
            }, i.toString());
            clockFace.appendChild(number);
        }
    }

    /**
     * 启动时钟
     */
    start() {
        if (this.isRunning) return;
        
        this.updateClock();
        this.clockInterval = setInterval(() => this.updateClock(), 1000);
        this.isRunning = true;
    }

    /**
     * 停止时钟
     */
    stop() {
        if (this.clockInterval) {
            clearInterval(this.clockInterval);
            this.clockInterval = null;
            this.isRunning = false;
        }
    }

    /**
     * 重启时钟
     */
    restart() {
        this.stop();
        this.start();
    }

    /**
     * 更新时钟显示
     */
    updateClock() {
        const now = new Date();
        
        // 更新数字时钟
        this.updateDigitalClock(now);
        
        // 更新模拟时钟
        this.updateAnalogClock(now);
    }

    /**
     * 更新数字时钟
     * @param {Date} now - 当前时间
     */
    updateDigitalClock(now) {
        if (!this.elements.digitalTime) return;

        const newTimeString = DateHelper.formatDateTime(now);
        
        // 检查时间是否真的改变了
        if (this.elements.digitalTime.textContent !== newTimeString) {
            this.elements.digitalTime.classList.add('updating');
            this.elements.digitalTime.textContent = newTimeString;
            
            setTimeout(() => {
                this.elements.digitalTime.classList.remove('updating');
            }, 300);
        }
    }

    /**
     * 更新模拟时钟
     * @param {Date} now - 当前时间
     */
    updateAnalogClock(now) {
        const hours = now.getHours() % 12;
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();
        
        // 计算角度
        const hourAngle = (hours * 30) + (minutes * 0.5);
        const minuteAngle = minutes * 6;
        const secondAngle = seconds * 6;
        
        // 获取指针元素
        const hourHand = DOMHelper.getElement('.hour-hand');
        const minuteHand = DOMHelper.getElement('.minute-hand');
        const secondHand = DOMHelper.getElement('.second-hand');
        
        // 更新指针位置
        if (hourHand) {
            hourHand.style.transform = `rotate(${hourAngle}deg)`;
        }
        if (minuteHand) {
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
        }
        if (secondHand) {
            // 为秒针添加平滑过渡，但避免从59秒到0秒的反向旋转
            const currentTransform = secondHand.style.transform;
            const currentAngle = currentTransform ? 
                parseFloat(currentTransform.match(/rotate\(([^)]+)deg\)/)?.[1] || 0) : 0;
            
            // 如果从大角度跳到小角度（如从354度到0度），暂时移除过渡
            if (currentAngle > 300 && secondAngle < 60) {
                secondHand.style.transition = 'none';
                secondHand.style.transform = `rotate(${secondAngle}deg)`;
                // 强制重绘后恢复过渡
                secondHand.offsetHeight;
                secondHand.style.transition = 'transform 0.1s ease-out';
            } else {
                secondHand.style.transform = `rotate(${secondAngle}deg)`;
            }
        }
    }

    /**
     * 设置时钟主题
     * @param {string} theme - 主题名称
     */
    setTheme(theme) {
        const clockElement = DOMHelper.getElement('.analog-clock');
        if (clockElement) {
            clockElement.setAttribute('data-theme', theme);
        }
    }

    /**
     * 获取当前时间信息
     * @returns {Object} 时间信息对象
     */
    getCurrentTime() {
        const now = new Date();
        return {
            timestamp: now.getTime(),
            formatted: DateHelper.formatDateTime(now),
            hours: now.getHours(),
            minutes: now.getMinutes(),
            seconds: now.getSeconds(),
            date: now.toDateString(),
            time: now.toTimeString(),
            iso: now.toISOString()
        };
    }

    /**
     * 设置时钟样式
     * @param {Object} styles - 样式对象
     */
    setClockStyles(styles) {
        const clockElement = DOMHelper.getElement('.analog-clock');
        if (clockElement && styles) {
            Object.entries(styles).forEach(([property, value]) => {
                clockElement.style[property] = value;
            });
        }
    }

    /**
     * 添加时钟事件监听器
     * @param {string} event - 事件类型
     * @param {Function} callback - 回调函数
     */
    addEventListener(event, callback) {
        switch (event) {
            case 'tick':
                // 每秒触发的事件
                this.onTick = callback;
                break;
            case 'minute':
                // 每分钟触发的事件
                this.onMinute = callback;
                break;
            case 'hour':
                // 每小时触发的事件
                this.onHour = callback;
                break;
        }
    }

    /**
     * 移除时钟事件监听器
     * @param {string} event - 事件类型
     */
    removeEventListener(event) {
        switch (event) {
            case 'tick':
                this.onTick = null;
                break;
            case 'minute':
                this.onMinute = null;
                break;
            case 'hour':
                this.onHour = null;
                break;
        }
    }

    /**
     * 触发时钟事件
     * @param {Date} now - 当前时间
     */
    triggerEvents(now) {
        // 每秒事件
        if (this.onTick) {
            this.onTick(now);
        }

        // 每分钟事件（秒数为0时）
        if (now.getSeconds() === 0 && this.onMinute) {
            this.onMinute(now);
        }

        // 每小时事件（分钟和秒数都为0时）
        if (now.getMinutes() === 0 && now.getSeconds() === 0 && this.onHour) {
            this.onHour(now);
        }
    }

    /**
     * 获取时钟状态
     * @returns {Object} 时钟状态信息
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            interval: this.clockInterval !== null,
            currentTime: this.getCurrentTime()
        };
    }

    /**
     * 销毁时钟管理器
     */
    destroy() {
        this.stop();
        this.onTick = null;
        this.onMinute = null;
        this.onHour = null;
        
        // 清理DOM元素
        const clockFace = DOMHelper.getElement('.clock-face');
        if (clockFace) {
            const marks = clockFace.querySelectorAll('.hour-mark, .minute-mark, .clock-number');
            marks.forEach(mark => mark.remove());
        }
    }

    /**
     * 格式化时间显示
     * @param {Date} date - 日期对象
     * @param {string} format - 格式类型
     * @returns {string} 格式化后的时间字符串
     */
    formatTime(date, format = 'default') {
        switch (format) {
            case 'time-only':
                return date.toLocaleTimeString('zh-CN');
            case 'date-only':
                return date.toLocaleDateString('zh-CN');
            case 'iso':
                return date.toISOString();
            case 'timestamp':
                return date.getTime().toString();
            default:
                return DateHelper.formatDateTime(date);
        }
    }

    /**
     * 设置时钟更新频率
     * @param {number} interval - 更新间隔（毫秒）
     */
    setUpdateInterval(interval) {
        if (interval < 100) {
            console.warn('时钟更新间隔不能小于100毫秒');
            return;
        }

        this.stop();
        this.updateClock();
        this.clockInterval = setInterval(() => this.updateClock(), interval);
        this.isRunning = true;
    }
}
