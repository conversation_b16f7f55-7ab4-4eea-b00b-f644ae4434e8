/**
 * 通用辅助函数
 * @fileoverview 提供通用的工具函数和辅助方法
 */

import { TODO_CONFIG, MESSAGE_TYPES, COUNTRY_TEMP_RANGES } from '../constants/config.js';

/**
 * DOM操作辅助函数
 */
export class DOMHelper {
    /**
     * 安全地获取DOM元素
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素，默认为document
     * @returns {Element|null} DOM元素或null
     */
    static getElement(selector, parent = document) {
        try {
            return parent.querySelector(selector);
        } catch (error) {
            console.error(`获取元素失败: ${selector}`, error);
            return null;
        }
    }

    /**
     * 安全地获取多个DOM元素
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素，默认为document
     * @returns {NodeList} DOM元素列表
     */
    static getElements(selector, parent = document) {
        try {
            return parent.querySelectorAll(selector);
        } catch (error) {
            console.error(`获取元素列表失败: ${selector}`, error);
            return [];
        }
    }

    /**
     * 创建DOM元素
     * @param {string} tagName - 标签名
     * @param {Object} attributes - 属性对象
     * @param {string} textContent - 文本内容
     * @returns {Element} 创建的DOM元素
     */
    static createElement(tagName, attributes = {}, textContent = '') {
        const element = document.createElement(tagName);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else {
                element.setAttribute(key, value);
            }
        });

        if (textContent) {
            element.textContent = textContent;
        }

        return element;
    }

    /**
     * 添加事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 事件选项
     */
    static addEventListener(element, event, handler, options = {}) {
        if (element && typeof handler === 'function') {
            element.addEventListener(event, handler, options);
        }
    }

    /**
     * 移除事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    static removeEventListener(element, event, handler) {
        if (element && typeof handler === 'function') {
            element.removeEventListener(event, handler);
        }
    }
}

/**
 * 字符串处理辅助函数
 */
export class StringHelper {
    /**
     * HTML转义
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    static escapeHtml(text) {
        if (typeof text !== 'string') {
            return '';
        }
        
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 截断文本
     * @param {string} text - 原始文本
     * @param {number} maxLength - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的文本
     */
    static truncate(text, maxLength = 50, suffix = '...') {
        if (typeof text !== 'string') {
            return '';
        }
        
        if (text.length <= maxLength) {
            return text;
        }
        
        return text.substring(0, maxLength - suffix.length) + suffix;
    }

    /**
     * 首字母大写
     * @param {string} text - 原始文本
     * @returns {string} 首字母大写的文本
     */
    static capitalize(text) {
        if (typeof text !== 'string' || text.length === 0) {
            return '';
        }
        
        return text.charAt(0).toUpperCase() + text.slice(1);
    }

    /**
     * 生成随机ID
     * @param {number} length - ID长度
     * @returns {string} 随机ID
     */
    static generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
}

/**
 * 日期时间处理辅助函数
 */
export class DateHelper {
    /**
     * 格式化日期时间
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} locale - 地区设置
     * @returns {string} 格式化后的日期时间
     */
    static formatDateTime(date, locale = 'zh-CN') {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            return dateObj.toLocaleString(locale);
        } catch (error) {
            console.error('日期格式化失败:', error);
            return '';
        }
    }

    /**
     * 格式化相对时间
     * @param {Date|string} date - 日期对象或字符串
     * @returns {string} 相对时间描述
     */
    static formatRelativeTime(date) {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            const now = new Date();
            const diff = now - dateObj;
            
            const seconds = Math.floor(diff / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) {
                return `${days}天前`;
            } else if (hours > 0) {
                return `${hours}小时前`;
            } else if (minutes > 0) {
                return `${minutes}分钟前`;
            } else {
                return '刚刚';
            }
        } catch (error) {
            console.error('相对时间格式化失败:', error);
            return '';
        }
    }

    /**
     * 检查日期是否过期
     * @param {Date|string} date - 日期对象或字符串
     * @returns {boolean} 是否过期
     */
    static isOverdue(date) {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            return dateObj < new Date();
        } catch (error) {
            console.error('日期检查失败:', error);
            return false;
        }
    }

    /**
     * 获取今天的开始时间
     * @returns {Date} 今天的开始时间
     */
    static getTodayStart() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
    }

    /**
     * 获取今天的结束时间
     * @returns {Date} 今天的结束时间
     */
    static getTodayEnd() {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
    }
}

/**
 * 数据处理辅助函数
 */
export class DataHelper {
    /**
     * 获取分类名称
     * @param {string} category - 分类键
     * @returns {string} 分类名称
     */
    static getCategoryName(category) {
        return TODO_CONFIG.CATEGORIES[category] || category;
    }

    /**
     * 获取优先级名称
     * @param {string} priority - 优先级键
     * @returns {string} 优先级名称
     */
    static getPriorityName(priority) {
        return TODO_CONFIG.PRIORITIES[priority] || priority;
    }

    /**
     * 获取过滤器名称
     * @param {string} filter - 过滤器键
     * @returns {string} 过滤器名称
     */
    static getFilterName(filter) {
        return TODO_CONFIG.FILTERS[filter] || filter;
    }

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    static deepClone(obj) {
        try {
            return JSON.parse(JSON.stringify(obj));
        } catch (error) {
            console.error('深拷贝失败:', error);
            return obj;
        }
    }

    /**
     * 合并对象
     * @param {Object} target - 目标对象
     * @param {...Object} sources - 源对象
     * @returns {Object} 合并后的对象
     */
    static merge(target, ...sources) {
        return Object.assign({}, target, ...sources);
    }

    /**
     * 检查对象是否为空
     * @param {any} obj - 要检查的对象
     * @returns {boolean} 是否为空
     */
    static isEmpty(obj) {
        if (obj == null) return true;
        if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
        if (typeof obj === 'object') return Object.keys(obj).length === 0;
        return false;
    }
}

/**
 * UI辅助函数
 */
export class UIHelper {
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     * @param {number} duration - 显示时长
     */
    static showMessage(message, type = 'success', duration = 3000) {
        const colors = MESSAGE_TYPES;
        
        const messageEl = DOMHelper.createElement('div', {
            className: 'message',
            style: `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.success};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideIn 0.3s ease-out;
            `
        }, message);

        // 添加样式（如果不存在）
        if (!document.querySelector('#message-styles')) {
            const style = DOMHelper.createElement('style', { id: 'message-styles' });
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            messageEl.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, duration);
    }

    /**
     * 数字动画
     * @param {Element} element - 目标元素
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} duration - 动画时长
     */
    static animateNumber(element, start, end, duration = 1000) {
        if (!element) return;
        
        element.classList.add('updating');
        const startTime = performance.now();
        const difference = end - start;

        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (difference * easeOutQuart));
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                setTimeout(() => {
                    element.classList.remove('updating');
                }, 100);
            }
        };

        requestAnimationFrame(updateNumber);
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

/**
 * 天气辅助函数
 */
export class WeatherHelper {
    /**
     * 获取天气类型样式类
     * @param {string} description - 天气描述
     * @returns {string} 样式类名
     */
    static getWeatherClass(description) {
        const desc = description.toLowerCase();
        if (desc.includes('晴') || desc.includes('sunny')) return 'sunny';
        if (desc.includes('云') || desc.includes('cloud')) return 'cloudy';
        if (desc.includes('阴') || desc.includes('overcast')) return 'overcast';
        if (desc.includes('雨') || desc.includes('rain')) return 'rainy';
        return 'default';
    }

    /**
     * 生成模拟天气数据
     * @param {string} cityName - 城市名称
     * @param {string} country - 国家
     * @returns {Object} 模拟天气数据
     */
    static generateMockWeatherData(cityName, country) {
        const tempRange = COUNTRY_TEMP_RANGES[country] || { min: 10, max: 30 };
        
        const weatherConditions = [
            { desc: '晴朗', icon: '☀️', temp: Math.floor(Math.random() * (tempRange.max - tempRange.min + 5)) + tempRange.max - 5 },
            { desc: '多云', icon: '⛅', temp: Math.floor(Math.random() * (tempRange.max - tempRange.min + 3)) + tempRange.max - 8 },
            { desc: '阴天', icon: '☁️', temp: Math.floor(Math.random() * (tempRange.max - tempRange.min + 2)) + tempRange.min },
            { desc: '小雨', icon: '🌧️', temp: Math.floor(Math.random() * (tempRange.max - tempRange.min)) + tempRange.min - 2 }
        ];

        const condition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
        
        return {
            city: cityName,
            temperature: condition.temp,
            description: condition.desc,
            humidity: Math.floor(Math.random() * 40) + 40,
            windSpeed: Math.floor(Math.random() * 10) + 1,
            pressure: Math.floor(Math.random() * 50) + 1000,
            icon: condition.icon,
            feelsLike: condition.temp + Math.floor(Math.random() * 6) - 3,
            uvIndex: Math.floor(Math.random() * 11)
        };
    }
}
