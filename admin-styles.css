/* 管理员后台样式 */

/* 重置管理员页面的body样式 */
body.admin-page {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    overflow: hidden;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.login-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.login-form {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
    position: relative;
    z-index: 1;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.login-header h1 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 28px;
    font-weight: 700;
}

.login-header p {
    margin: 0 0 30px 0;
    color: #666;
    font-size: 16px;
}

.input-group {
    margin-bottom: 20px;
    text-align: left;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.input-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.privacy-notice {
    margin-top: 20px;
    color: #888;
}

/* 仪表板样式 */
.dashboard-container {
    min-height: 100vh;
    background: #f8fafc;
    position: relative;
    z-index: 1;
}

.admin-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
    margin: 0;
    color: #1a202c;
    font-size: 24px;
    font-weight: 700;
}

.version {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 12px;
}

.header-right {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.admin-main {
    padding: 24px;
}

/* 标签页导航 */
.tab-nav {
    display: flex;
    background: white;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-btn:hover:not(.active) {
    background: #f1f5f9;
    color: #334155;
}

/* 概览页面样式 */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
    margin: 0 0 20px 0;
    color: #1a202c;
    font-size: 18px;
    font-weight: 600;
}

.chart-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-row {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.chart-row:last-child {
    border-bottom: none;
}

.chart-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.chart-label {
    flex: 1;
    font-size: 14px;
    color: #4a5568;
}

.chart-value {
    font-size: 14px;
    font-weight: 600;
    color: #1a202c;
}

.no-data {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 40px;
}

/* 用户管理页面样式 */
.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.users-header h2 {
    margin: 0;
    color: #1a202c;
    font-size: 24px;
    font-weight: 700;
}

.users-actions {
    display: flex;
    gap: 12px;
}

.users-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 1fr;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #4a5568;
    font-size: 14px;
}

.table-cell {
    padding: 16px;
    border-right: 1px solid #e2e8f0;
}

.table-cell:last-child {
    border-right: none;
}

.user-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 1fr;
    border-bottom: 1px solid #f1f5f9;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.user-row:hover {
    background: #f8fafc;
}

.user-row:last-child {
    border-bottom: none;
}

.user-row > div {
    padding: 16px;
    border-right: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
}

.user-row > div:last-child {
    border-right: none;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-small:hover {
    background: #cbd5e0;
}

/* 数据分析页面样式 */
.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.analytics-header h2 {
    margin: 0;
    color: #1a202c;
    font-size: 24px;
    font-weight: 700;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
}

.analytics-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analytics-card h3 {
    margin: 0 0 16px 0;
    color: #1a202c;
    font-size: 18px;
    font-weight: 600;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-name {
    color: #64748b;
    font-size: 14px;
}

.stat-value {
    color: #1a202c;
    font-weight: 600;
    font-size: 14px;
}

.privacy-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.privacy-info p {
    margin: 0;
    font-size: 14px;
    color: #059669;
}

/* 系统信息页面样式 */
.system-header h2 {
    margin: 0 0 24px 0;
    color: #1a202c;
    font-size: 24px;
    font-weight: 700;
}

.system-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
}

.system-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.system-card h3 {
    margin: 0 0 16px 0;
    color: #1a202c;
    font-size: 18px;
    font-weight: 600;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #64748b;
    font-size: 14px;
}

.info-value {
    color: #1a202c;
    font-weight: 600;
    font-size: 14px;
}

.security-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #4a5568;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-indicator.success {
    background: #10b981;
}

.status-indicator.warning {
    background: #f59e0b;
}

.status-indicator.error {
    background: #ef4444;
}

.privacy-policy {
    font-size: 14px;
    line-height: 1.6;
    color: #4a5568;
}

.privacy-policy p {
    margin: 0 0 12px 0;
}

.privacy-policy ul {
    margin: 8px 0 16px 0;
    padding-left: 20px;
}

.privacy-policy li {
    margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .tab-nav {
        flex-direction: column;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .table-header,
    .user-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .users-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .analytics-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
}
