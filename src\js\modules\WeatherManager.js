/**
 * 天气管理模块
 * @fileoverview 负责天气数据获取、显示和缓存管理
 */

import { WeatherApiClient } from '../utils/api.js';
import { WeatherStorage, SettingsStorage } from '../utils/storage.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UIHelper, WeatherHelper } from '../utils/helpers.js';
import { REGIONS_DATA, CITY_NAMES, API_CONFIG } from '../constants/config.js';

/**
 * 天气管理器
 */
export class WeatherManager {
    /**
     * 构造函数
     * @param {Object} elements - DOM元素对象
     */
    constructor(elements) {
        this.elements = elements;
        this.weatherCache = WeatherStorage.load();
        this.weatherInterval = null;
        this.isUpdating = false;
        
        this.init();
    }

    /**
     * 初始化天气管理器
     */
    init() {
        this.initRegionSelect();
        this.bindEvents();
        this.updateWeather();
        this.startAutoRefresh();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 国家选择变化事件
        DOMHelper.addEventListener(this.elements.countrySelect, 'change', () => {
            this.onCountryChange();
        });

        // 地区选择变化事件
        DOMHelper.addEventListener(this.elements.regionSelect, 'change', () => {
            this.updateWeather();
        });

        // 刷新天气按钮事件
        DOMHelper.addEventListener(this.elements.refreshWeather, 'click', () => {
            this.updateWeather(true);
        });
    }

    /**
     * 初始化地区选择器
     */
    initRegionSelect() {
        this.updateRegionSelect();
    }

    /**
     * 国家选择变化处理
     */
    onCountryChange() {
        this.updateRegionSelect();
        this.updateWeather();
    }

    /**
     * 更新地区选择器
     */
    updateRegionSelect() {
        const country = this.elements.countrySelect.value;
        const regions = this.getRegionsByCountry(country);
        
        this.elements.regionSelect.innerHTML = '<option value="">选择地区</option>';
        regions.forEach(region => {
            const option = DOMHelper.createElement('option', {
                value: region.value
            }, region.name);
            this.elements.regionSelect.appendChild(option);
        });

        // 如果只有一个地区，自动选择
        if (regions.length === 1) {
            this.elements.regionSelect.value = regions[0].value;
        }
    }

    /**
     * 根据国家获取地区列表
     * @param {string} country - 国家代码
     * @returns {Array} 地区列表
     */
    getRegionsByCountry(country) {
        return REGIONS_DATA[country] || [];
    }

    /**
     * 更新天气信息
     * @param {boolean} forceRefresh - 是否强制刷新
     */
    async updateWeather(forceRefresh = false) {
        const country = this.elements.countrySelect.value;
        const region = this.elements.regionSelect.value;
        
        if (!country || !region) {
            this.showWeatherError('请选择国家和地区');
            return;
        }

        if (this.isUpdating) {
            return;
        }

        const locationKey = `${country}-${region}`;
        this.showWeatherLoading();
        this.addRefreshButtonLoading();
        this.isUpdating = true;

        try {
            // 检查缓存
            if (!forceRefresh) {
                const cached = WeatherStorage.getLocationCache(locationKey);
                if (cached) {
                    this.displayWeather(cached);
                    this.removeRefreshButtonLoading();
                    this.isUpdating = false;
                    return;
                }
            }

            // 获取天气数据
            const weatherData = await this.fetchWeatherData(country, region);
            this.displayWeather(weatherData);
            this.removeRefreshButtonLoading();
            
            // 保存到缓存
            WeatherStorage.setLocationCache(locationKey, weatherData);
            
        } catch (error) {
            console.error('获取天气数据失败:', error);
            
            // 如果是API密钥相关错误，显示模拟数据
            if (error.message.includes('API密钥') || error.message.includes('401')) {
                const cityName = CITY_NAMES[region] || region;
                const mockData = WeatherHelper.generateMockWeatherData(cityName, country);
                this.displayWeather(mockData);
                this.removeRefreshButtonLoading();
                
                UIHelper.showMessage('使用模拟天气数据，请在设置中配置API密钥获取真实数据', 'warning', 5000);
            } else {
                this.showWeatherError('获取天气信息失败，请稍后重试');
                this.removeRefreshButtonLoading();
            }
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * 获取天气数据
     * @param {string} country - 国家代码
     * @param {string} region - 地区代码
     * @returns {Promise<Object>} 天气数据
     */
    async fetchWeatherData(country, region) {
        const cityName = CITY_NAMES[region];
        if (!cityName) {
            throw new Error('不支持的地区');
        }

        // 从设置中获取API密钥和类型
        const apiKey = SettingsStorage.load('weatherApiKey', '');
        const apiType = SettingsStorage.load('weatherApiType', 'openweathermap');

        if (!apiKey) {
            throw new Error('API密钥未配置');
        }

        return await WeatherApiClient.getWeatherData(cityName, apiKey, apiType);
    }

    /**
     * 显示天气信息
     * @param {Object} data - 天气数据
     */
    displayWeather(data) {
        // 获取当前天气类型用于动画
        const currentWeatherClass = this.getCurrentWeatherClass();
        const newWeatherClass = WeatherHelper.getWeatherClass(data.description);
        
        // 如果天气类型改变，执行转换动画
        if (currentWeatherClass !== newWeatherClass) {
            this.animateWeatherTransition(currentWeatherClass, newWeatherClass, data);
        } else {
            this.updateWeatherDisplay(data);
        }
    }

    /**
     * 获取当前天气样式类
     * @returns {string} 天气样式类
     */
    getCurrentWeatherClass() {
        if (!this.elements.weatherDisplay) return 'default';
        const classes = this.elements.weatherDisplay.className;
        if (classes.includes('sunny')) return 'sunny';
        if (classes.includes('cloudy')) return 'cloudy';
        if (classes.includes('overcast')) return 'overcast';
        if (classes.includes('rainy')) return 'rainy';
        return 'default';
    }

    /**
     * 天气转换动画
     * @param {string} fromClass - 原天气类型
     * @param {string} toClass - 新天气类型
     * @param {Object} data - 天气数据
     */
    animateWeatherTransition(fromClass, toClass, data) {
        // 添加转换类
        this.elements.weatherDisplay.classList.add('weather-transition');
        
        // 执行背景转换
        this.elements.weatherDisplay.className = `weather-display ${toClass} weather-transition`;
        
        // 创建天气场景元素
        this.createWeatherScene(fromClass, toClass);
        
        // 延迟更新内容，让动画先播放
        setTimeout(() => {
            this.updateWeatherDisplay(data);
        }, 500);

        // 清理动画类
        setTimeout(() => {
            this.elements.weatherDisplay.classList.remove('weather-transition');
            this.cleanupWeatherScene();
        }, 1500);
    }

    /**
     * 创建天气场景动画
     * @param {string} fromClass - 原天气类型
     * @param {string} toClass - 新天气类型
     */
    createWeatherScene(fromClass, toClass) {
        // 清理现有场景
        this.cleanupWeatherScene();
        
        const scene = DOMHelper.createElement('div', { className: 'weather-scene' });
        
        // 根据天气类型创建相应的动画元素
        if (fromClass === 'sunny' && (toClass === 'cloudy' || toClass === 'overcast')) {
            // 太阳向上移动消失
            const sun = DOMHelper.createElement('div', {
                className: 'weather-element sun sun-exit'
            }, '☀️');
            scene.appendChild(sun);
            
            // 云朵从下方进入
            const cloud1 = DOMHelper.createElement('div', {
                className: 'weather-element cloud cloud-1 cloud-enter'
            }, '☁️');
            scene.appendChild(cloud1);
            
            const cloud2 = DOMHelper.createElement('div', {
                className: 'weather-element cloud cloud-2 cloud-enter'
            }, '☁️');
            scene.appendChild(cloud2);
            
            // 如果转为阴天，云朵变灰
            if (toClass === 'overcast') {
                setTimeout(() => {
                    cloud1.classList.add('cloud-gray');
                    cloud2.classList.add('cloud-gray');
                }, 500);
            }
        }
        
        if (toClass === 'rainy') {
            // 添加雨滴效果
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const rainDrop = DOMHelper.createElement('div', {
                        className: 'weather-element rain-drop rain-fall',
                        style: `
                            left: ${Math.random() * 80 + 10}%;
                            animation-delay: ${Math.random() * 0.5}s;
                        `
                    }, '💧');
                    scene.appendChild(rainDrop);
                }, i * 100);
            }
        }
        
        this.elements.weatherDisplay.appendChild(scene);
    }

    /**
     * 清理天气场景
     */
    cleanupWeatherScene() {
        const scene = this.elements.weatherDisplay.querySelector('.weather-scene');
        if (scene) {
            scene.remove();
        }
    }

    /**
     * 更新天气显示内容
     * @param {Object} data - 天气数据
     */
    updateWeatherDisplay(data) {
        // 添加体感温度和UV指数（如果可用）
        let extraInfo = '';
        if (data.feelsLike !== undefined && data.feelsLike !== data.temperature) {
            extraInfo += `<div class="weather-detail">
                <span>🌡️</span>
                <span>体感: ${data.feelsLike}°C</span>
            </div>`;
        }
        if (data.uvIndex !== undefined && data.uvIndex > 0) {
            extraInfo += `<div class="weather-detail">
                <span>☀️</span>
                <span>UV: ${data.uvIndex}</span>
            </div>`;
        }

        this.elements.weatherDisplay.innerHTML = `
            <div class="weather-info">
                <div class="weather-main">
                    <span class="weather-icon animated">${data.icon}</span>
                    <span class="weather-temp changing">${data.temperature}°C</span>
                </div>
                <div class="weather-desc">${data.description}</div>
                <div class="weather-details">
                    <div class="weather-detail">
                        <span>💧</span>
                        <span>${data.humidity}%</span>
                    </div>
                    <div class="weather-detail">
                        <span>💨</span>
                        <span>${data.windSpeed}m/s</span>
                    </div>
                    <div class="weather-detail">
                        <span>🔽</span>
                        <span>${data.pressure}hPa</span>
                    </div>
                    ${extraInfo}
                </div>
            </div>
        `;

        // 设置天气背景类
        const weatherClass = WeatherHelper.getWeatherClass(data.description);
        this.elements.weatherDisplay.className = `weather-display ${weatherClass}`;

        // 移除动画类，准备下次使用
        setTimeout(() => {
            const icon = this.elements.weatherDisplay.querySelector('.weather-icon');
            const temp = this.elements.weatherDisplay.querySelector('.weather-temp');
            if (icon) icon.classList.remove('animated');
            if (temp) temp.classList.remove('changing');
        }, 600);
    }

    /**
     * 显示天气加载状态
     */
    showWeatherLoading() {
        this.elements.weatherDisplay.innerHTML = '<div class="weather-loading">加载中...</div>';
    }

    /**
     * 显示天气错误信息
     * @param {string} message - 错误消息
     */
    showWeatherError(message) {
        this.elements.weatherDisplay.innerHTML = `<div class="weather-error">${message}</div>`;
    }

    /**
     * 添加刷新按钮加载状态
     */
    addRefreshButtonLoading() {
        if (this.elements.refreshWeather) {
            this.elements.refreshWeather.classList.add('loading');
            this.elements.refreshWeather.disabled = true;
        }
    }

    /**
     * 移除刷新按钮加载状态
     */
    removeRefreshButtonLoading() {
        if (this.elements.refreshWeather) {
            this.elements.refreshWeather.classList.remove('loading');
            this.elements.refreshWeather.disabled = false;
        }
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        const refreshInterval = SettingsStorage.load('weatherRefreshInterval', '10');
        const intervalMs = parseInt(refreshInterval) * 60000; // 转换为毫秒
        
        this.weatherInterval = setInterval(() => {
            this.updateWeather();
        }, intervalMs);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.weatherInterval) {
            clearInterval(this.weatherInterval);
            this.weatherInterval = null;
        }
    }

    /**
     * 更新刷新间隔
     * @param {number} interval - 刷新间隔（分钟）
     */
    updateRefreshInterval(interval) {
        this.stopAutoRefresh();
        
        const intervalMs = parseInt(interval) * 60000;
        this.weatherInterval = setInterval(() => {
            this.updateWeather();
        }, intervalMs);
    }

    /**
     * 清空天气缓存
     */
    clearCache() {
        WeatherStorage.clear();
        this.weatherCache = {};
        UIHelper.showMessage('天气缓存已清空');
    }

    /**
     * 获取天气管理器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isUpdating: this.isUpdating,
            hasInterval: this.weatherInterval !== null,
            cacheSize: Object.keys(this.weatherCache).length,
            currentLocation: {
                country: this.elements.countrySelect.value,
                region: this.elements.regionSelect.value
            }
        };
    }

    /**
     * 销毁天气管理器
     */
    destroy() {
        this.stopAutoRefresh();
        this.cleanupWeatherScene();
        this.isUpdating = false;
    }
}
