/**
 * 安全管理模块
 * @fileoverview 负责权限控制、数据加密和安全访问管理
 */

import { Storage } from '../utils/storage.js';
import { STORAGE_KEYS, ADMIN_CONFIG } from '../constants/config.js';
import { StringHelper, DateHelper } from '../utils/helpers.js';

/**
 * 安全管理器
 */
export class SecurityManager {
    /**
     * 构造函数
     */
    constructor() {
        this.isAdminAuthenticated = false;
        this.adminSessionExpiry = null;
        this.encryptionKey = null;
        
        this.init();
    }

    /**
     * 初始化安全管理器
     */
    init() {
        console.log('SecurityManager: 初始化安全管理器');
        console.log('SecurityManager: 默认管理员密码:', ADMIN_CONFIG.DEFAULT_ADMIN_PASSWORD);
        this.checkAdminSession();
        this.generateEncryptionKey();
    }

    /**
     * 检查管理员会话
     */
    checkAdminSession() {
        const adminAuth = Storage.load(STORAGE_KEYS.adminAuth);
        if (adminAuth && adminAuth.expiry > Date.now()) {
            this.isAdminAuthenticated = true;
            this.adminSessionExpiry = adminAuth.expiry;
        } else {
            this.clearAdminSession();
        }
    }

    /**
     * 管理员登录
     * @param {string} password - 管理员密码
     * @returns {boolean} 是否登录成功
     */
    async adminLogin(password) {
        try {
            console.log('SecurityManager: 开始管理员登录验证');
            console.log('SecurityManager: 默认密码配置:', ADMIN_CONFIG.DEFAULT_ADMIN_PASSWORD);
            
            // 验证密码
            const hashedPassword = await this.hashPassword(password);
            const storedHash = await this.hashPassword(ADMIN_CONFIG.DEFAULT_ADMIN_PASSWORD);
            
            console.log('SecurityManager: 密码哈希对比:', {
                inputHash: hashedPassword,
                storedHash: storedHash,
                match: hashedPassword === storedHash
            });
            
            if (hashedPassword === storedHash) {
                console.log('SecurityManager: 密码验证成功，创建会话');
                
                // 创建管理员会话
                const expiry = Date.now() + ADMIN_CONFIG.SESSION_TIMEOUT;
                const sessionData = {
                    authenticated: true,
                    expiry: expiry,
                    loginTime: Date.now(),
                    sessionId: StringHelper.generateId(32)
                };
                
                console.log('SecurityManager: 会话数据:', sessionData);
                
                const saved = Storage.save(STORAGE_KEYS.adminAuth, sessionData);
                console.log('SecurityManager: 会话保存结果:', saved);
                
                this.isAdminAuthenticated = true;
                this.adminSessionExpiry = expiry;
                
                // 记录登录日志
                this.logSecurityEvent('ADMIN_LOGIN', {
                    success: true,
                    timestamp: Date.now(),
                    sessionId: sessionData.sessionId
                });
                
                return true;
            } else {
                console.log('SecurityManager: 密码验证失败');
                // 记录失败的登录尝试
                this.logSecurityEvent('ADMIN_LOGIN_FAILED', {
                    timestamp: Date.now(),
                    reason: 'Invalid password'
                });
                
                return false;
            }
        } catch (error) {
            console.error('SecurityManager: 管理员登录失败:', error);
            return false;
        }
    }

    /**
     * 管理员登出
     */
    adminLogout() {
        this.clearAdminSession();
        this.logSecurityEvent('ADMIN_LOGOUT', {
            timestamp: Date.now()
        });
    }

    /**
     * 清除管理员会话
     */
    clearAdminSession() {
        Storage.remove(STORAGE_KEYS.adminAuth);
        this.isAdminAuthenticated = false;
        this.adminSessionExpiry = null;
    }

    /**
     * 检查管理员权限
     * @returns {boolean} 是否有管理员权限
     */
    hasAdminAccess() {
        if (!this.isAdminAuthenticated) {
            return false;
        }
        
        // 检查会话是否过期
        if (this.adminSessionExpiry && Date.now() > this.adminSessionExpiry) {
            this.clearAdminSession();
            return false;
        }
        
        return true;
    }

    /**
     * 密码哈希
     * @param {string} password - 原始密码
     * @returns {Promise<string>} 哈希后的密码
     */
    async hashPassword(password) {
        if (crypto && crypto.subtle) {
            const encoder = new TextEncoder();
            const data = encoder.encode(password + 'salt_bigbluebook_2024');
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } else {
            // 降级到简单哈希
            let hash = 0;
            const saltedPassword = password + 'salt_bigbluebook_2024';
            for (let i = 0; i < saltedPassword.length; i++) {
                const char = saltedPassword.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16);
        }
    }

    /**
     * 生成加密密钥
     */
    generateEncryptionKey() {
        // 生成简单的加密密钥（用于数据混淆）
        this.encryptionKey = StringHelper.generateId(32);
    }

    /**
     * 数据加密（简单的XOR加密）
     * @param {string} data - 要加密的数据
     * @returns {string} 加密后的数据
     */
    encryptData(data) {
        if (!this.encryptionKey) {
            return data;
        }
        
        let encrypted = '';
        for (let i = 0; i < data.length; i++) {
            const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
            const dataChar = data.charCodeAt(i);
            encrypted += String.fromCharCode(dataChar ^ keyChar);
        }
        
        return btoa(encrypted); // Base64编码
    }

    /**
     * 数据解密
     * @param {string} encryptedData - 加密的数据
     * @returns {string} 解密后的数据
     */
    decryptData(encryptedData) {
        if (!this.encryptionKey) {
            return encryptedData;
        }
        
        try {
            const encrypted = atob(encryptedData); // Base64解码
            let decrypted = '';
            
            for (let i = 0; i < encrypted.length; i++) {
                const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
                const encryptedChar = encrypted.charCodeAt(i);
                decrypted += String.fromCharCode(encryptedChar ^ keyChar);
            }
            
            return decrypted;
        } catch (error) {
            console.error('数据解密失败:', error);
            return encryptedData;
        }
    }

    /**
     * 数据脱敏
     * @param {Object} data - 原始数据
     * @param {Array} sensitiveFields - 敏感字段列表
     * @returns {Object} 脱敏后的数据
     */
    sanitizeData(data, sensitiveFields = ['todoText', 'userAgent', 'ipAddress']) {
        const sanitized = { ...data };
        
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                switch (field) {
                    case 'todoText':
                        sanitized[field] = `[${sanitized[field].length} characters]`;
                        break;
                    case 'userAgent':
                        sanitized[field] = sanitized[field].substring(0, 50) + '...';
                        break;
                    case 'ipAddress':
                        const ipParts = sanitized[field].split('.');
                        if (ipParts.length === 4) {
                            sanitized[field] = `${ipParts[0]}.${ipParts[1]}.xxx.xxx`;
                        }
                        break;
                    default:
                        sanitized[field] = '[REDACTED]';
                }
            }
        });
        
        return sanitized;
    }

    /**
     * 记录安全事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    logSecurityEvent(eventType, eventData) {
        const logEntry = {
            id: StringHelper.generateId(16),
            type: eventType,
            timestamp: Date.now(),
            data: eventData,
            userAgent: navigator.userAgent.substring(0, 100)
        };
        
        const logs = Storage.load(STORAGE_KEYS.systemLogs, []);
        logs.push(logEntry);
        
        // 限制日志大小
        if (logs.length > 1000) {
            logs.splice(0, logs.length - 1000);
        }
        
        Storage.save(STORAGE_KEYS.systemLogs, logs);
    }

    /**
     * 获取安全日志
     * @param {number} limit - 返回的日志数量限制
     * @returns {Array} 安全日志列表
     */
    getSecurityLogs(limit = 100) {
        if (!this.hasAdminAccess()) {
            return [];
        }
        
        const logs = Storage.load(STORAGE_KEYS.systemLogs, []);
        return logs.slice(-limit).reverse();
    }

    /**
     * 清理过期日志
     * @param {number} retentionDays - 保留天数
     */
    cleanupLogs(retentionDays = 30) {
        if (!this.hasAdminAccess()) {
            return;
        }
        
        const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);
        const logs = Storage.load(STORAGE_KEYS.systemLogs, []);
        const validLogs = logs.filter(log => log.timestamp > cutoffTime);
        
        if (validLogs.length !== logs.length) {
            Storage.save(STORAGE_KEYS.systemLogs, validLogs);
        }
    }

    /**
     * 验证数据完整性
     * @param {Object} data - 要验证的数据
     * @returns {boolean} 数据是否完整
     */
    validateDataIntegrity(data) {
        // 基本的数据完整性检查
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        // 检查必要字段
        const requiredFields = ['id', 'timestamp'];
        for (const field of requiredFields) {
            if (!data.hasOwnProperty(field)) {
                return false;
            }
        }
        
        // 检查时间戳合理性
        if (typeof data.timestamp !== 'number' || data.timestamp > Date.now()) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取管理员会话信息
     * @returns {Object} 会话信息
     */
    getAdminSessionInfo() {
        if (!this.hasAdminAccess()) {
            return null;
        }
        
        const adminAuth = Storage.load(STORAGE_KEYS.adminAuth);
        return {
            authenticated: this.isAdminAuthenticated,
            expiry: this.adminSessionExpiry,
            timeRemaining: this.adminSessionExpiry ? this.adminSessionExpiry - Date.now() : 0,
            sessionId: adminAuth ? adminAuth.sessionId : null
        };
    }

    /**
     * 更新管理员密码
     * @param {string} oldPassword - 旧密码
     * @param {string} newPassword - 新密码
     * @returns {Promise<boolean>} 是否更新成功
     */
    async updateAdminPassword(oldPassword, newPassword) {
        if (!this.hasAdminAccess()) {
            return false;
        }
        
        // 验证旧密码
        const oldHash = await this.hashPassword(oldPassword);
        const storedHash = await this.hashPassword(ADMIN_CONFIG.DEFAULT_ADMIN_PASSWORD);
        
        if (oldHash !== storedHash) {
            this.logSecurityEvent('PASSWORD_CHANGE_FAILED', {
                reason: 'Invalid old password',
                timestamp: Date.now()
            });
            return false;
        }
        
        // 这里在实际应用中应该更新存储的密码
        // 由于这是演示版本，我们只记录事件
        this.logSecurityEvent('PASSWORD_CHANGED', {
            timestamp: Date.now()
        });
        
        return true;
    }
}
