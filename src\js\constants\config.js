/**
 * 应用配置常量
 * @fileoverview 包含应用的所有配置常量和静态数据
 */

// API配置
export const API_CONFIG = {
    WEATHER_APIS: {
        openweathermap: {
            name: 'OpenWeatherMap',
            baseUrl: 'https://api.openweathermap.org/data/2.5/weather',
            testUrl: 'https://api.openweathermap.org/data/2.5/weather?q=Beijing&appid={apiKey}&units=metric&lang=zh_cn',
            helpUrl: 'https://openweathermap.org/api'
        },
        weatherapi: {
            name: 'WeatherAPI',
            baseUrl: 'https://api.weatherapi.com/v1/current.json',
            testUrl: 'https://api.weatherapi.com/v1/current.json?key={apiKey}&q=Beijing&lang=zh',
            helpUrl: 'https://www.weatherapi.com/'
        },
        weathermap: {
            name: 'WeatherMap',
            baseUrl: 'https://api.weathermap.org/data/2.5/weather',
            testUrl: 'https://api.weathermap.org/data/2.5/weather?q=Beijing&appid={apiKey}&units=metric&lang=zh_cn',
            helpUrl: 'https://weathermap.org/api'
        }
    },
    CACHE_DURATION: 600000, // 10分钟
    DEFAULT_REFRESH_INTERVAL: 600000 // 10分钟
};

// 待办事项配置
export const TODO_CONFIG = {
    PRIORITIES: {
        critical: '极高',
        high: '高',
        medium: '中',
        low: '低',
        lowest: '最低'
    },
    CATEGORIES: {
        work: '工作',
        personal: '个人',
        study: '学习',
        health: '健康'
    },
    // 预设分类列表（用于UI显示）
    PRESET_CATEGORIES: ['work', 'personal', 'study', 'health'],
    FILTERS: {
        all: '全部',
        active: '进行中',
        completed: '已完成'
    }
};

// 地区数据
export const REGIONS_DATA = {
    china: [
        { value: 'beijing', name: '北京' },
        { value: 'shanghai', name: '上海' },
        { value: 'guangzhou', name: '广州' },
        { value: 'shenzhen', name: '深圳' },
        { value: 'hangzhou', name: '杭州' },
        { value: 'nanjing', name: '南京' },
        { value: 'chengdu', name: '成都' },
        { value: 'wuhan', name: '武汉' },
        { value: 'xian', name: '西安' },
        { value: 'chongqing', name: '重庆' }
    ],
    usa: [
        { value: 'new-york', name: 'New York' },
        { value: 'los-angeles', name: 'Los Angeles' },
        { value: 'chicago', name: 'Chicago' },
        { value: 'houston', name: 'Houston' },
        { value: 'phoenix', name: 'Phoenix' },
        { value: 'philadelphia', name: 'Philadelphia' },
        { value: 'san-antonio', name: 'San Antonio' },
        { value: 'san-diego', name: 'San Diego' },
        { value: 'dallas', name: 'Dallas' },
        { value: 'san-jose', name: 'San Jose' }
    ],
    japan: [
        { value: 'tokyo', name: '东京' },
        { value: 'osaka', name: '大阪' },
        { value: 'kyoto', name: '京都' },
        { value: 'yokohama', name: '横滨' },
        { value: 'nagoya', name: '名古屋' },
        { value: 'sapporo', name: '札幌' },
        { value: 'fukuoka', name: '福冈' },
        { value: 'hiroshima', name: '广岛' }
    ],
    uk: [
        { value: 'london', name: 'London' },
        { value: 'manchester', name: 'Manchester' },
        { value: 'birmingham', name: 'Birmingham' },
        { value: 'liverpool', name: 'Liverpool' },
        { value: 'leeds', name: 'Leeds' },
        { value: 'glasgow', name: 'Glasgow' },
        { value: 'edinburgh', name: 'Edinburgh' },
        { value: 'bristol', name: 'Bristol' }
    ],
    france: [
        { value: 'paris', name: 'Paris' },
        { value: 'marseille', name: 'Marseille' },
        { value: 'lyon', name: 'Lyon' },
        { value: 'toulouse', name: 'Toulouse' },
        { value: 'nice', name: 'Nice' },
        { value: 'nantes', name: 'Nantes' },
        { value: 'strasbourg', name: 'Strasbourg' },
        { value: 'montpellier', name: 'Montpellier' }
    ],
    germany: [
        { value: 'berlin', name: 'Berlin' },
        { value: 'hamburg', name: 'Hamburg' },
        { value: 'munich', name: 'Munich' },
        { value: 'cologne', name: 'Cologne' },
        { value: 'frankfurt', name: 'Frankfurt' },
        { value: 'stuttgart', name: 'Stuttgart' },
        { value: 'dusseldorf', name: 'Düsseldorf' },
        { value: 'dortmund', name: 'Dortmund' }
    ],
    australia: [
        { value: 'sydney', name: 'Sydney' },
        { value: 'melbourne', name: 'Melbourne' },
        { value: 'brisbane', name: 'Brisbane' },
        { value: 'perth', name: 'Perth' },
        { value: 'adelaide', name: 'Adelaide' },
        { value: 'gold-coast', name: 'Gold Coast' },
        { value: 'newcastle', name: 'Newcastle' },
        { value: 'canberra', name: 'Canberra' }
    ],
    canada: [
        { value: 'toronto', name: 'Toronto' },
        { value: 'montreal', name: 'Montreal' },
        { value: 'vancouver', name: 'Vancouver' },
        { value: 'calgary', name: 'Calgary' },
        { value: 'ottawa', name: 'Ottawa' },
        { value: 'edmonton', name: 'Edmonton' },
        { value: 'mississauga', name: 'Mississauga' },
        { value: 'winnipeg', name: 'Winnipeg' }
    ]
};

// 城市名称映射
export const CITY_NAMES = {
    // 中国城市
    beijing: '北京',
    shanghai: '上海',
    guangzhou: '广州',
    shenzhen: '深圳',
    hangzhou: '杭州',
    nanjing: '南京',
    chengdu: '成都',
    wuhan: '武汉',
    xian: '西安',
    chongqing: '重庆',
    // 美国城市
    'new-york': 'New York',
    'los-angeles': 'Los Angeles',
    chicago: 'Chicago',
    houston: 'Houston',
    phoenix: 'Phoenix',
    philadelphia: 'Philadelphia',
    'san-antonio': 'San Antonio',
    'san-diego': 'San Diego',
    dallas: 'Dallas',
    'san-jose': 'San Jose',
    // 日本城市
    tokyo: '东京',
    osaka: '大阪',
    kyoto: '京都',
    yokohama: '横滨',
    nagoya: '名古屋',
    sapporo: '札幌',
    fukuoka: '福冈',
    hiroshima: '广岛',
    // 英国城市
    london: 'London',
    manchester: 'Manchester',
    birmingham: 'Birmingham',
    liverpool: 'Liverpool',
    leeds: 'Leeds',
    glasgow: 'Glasgow',
    edinburgh: 'Edinburgh',
    bristol: 'Bristol',
    // 法国城市
    paris: 'Paris',
    marseille: 'Marseille',
    lyon: 'Lyon',
    toulouse: 'Toulouse',
    nice: 'Nice',
    nantes: 'Nantes',
    strasbourg: 'Strasbourg',
    montpellier: 'Montpellier',
    // 德国城市
    berlin: 'Berlin',
    hamburg: 'Hamburg',
    munich: 'Munich',
    cologne: 'Cologne',
    frankfurt: 'Frankfurt',
    stuttgart: 'Stuttgart',
    dusseldorf: 'Düsseldorf',
    dortmund: 'Dortmund',
    // 澳大利亚城市
    sydney: 'Sydney',
    melbourne: 'Melbourne',
    brisbane: 'Brisbane',
    perth: 'Perth',
    adelaide: 'Adelaide',
    'gold-coast': 'Gold Coast',
    newcastle: 'Newcastle',
    canberra: 'Canberra',
    // 加拿大城市
    toronto: 'Toronto',
    montreal: 'Montreal',
    vancouver: 'Vancouver',
    calgary: 'Calgary',
    ottawa: 'Ottawa',
    edmonton: 'Edmonton',
    mississauga: 'Mississauga',
    winnipeg: 'Winnipeg'
};

// 天气图标映射
export const WEATHER_ICONS = {
    openweathermap: {
        '01d': '☀️', '01n': '🌙',
        '02d': '⛅', '02n': '☁️',
        '03d': '☁️', '03n': '☁️',
        '04d': '☁️', '04n': '☁️',
        '09d': '🌧️', '09n': '🌧️',
        '10d': '🌦️', '10n': '🌧️',
        '11d': '⛈️', '11n': '⛈️',
        '13d': '❄️', '13n': '❄️',
        '50d': '🌫️', '50n': '🌫️'
    },
    weatherapi: {
        '1000': '☀️', '1003': '⛅', '1006': '☁️', '1009': '☁️',
        '1030': '🌫️', '1063': '🌦️', '1066': '🌨️', '1069': '🌨️',
        '1072': '🌨️', '1087': '⛈️', '1114': '❄️', '1117': '❄️',
        '1135': '🌫️', '1147': '🌫️', '1150': '🌦️', '1153': '🌦️',
        '1168': '🌨️', '1171': '🌨️', '1180': '🌦️', '1183': '🌧️',
        '1186': '🌧️', '1189': '🌧️', '1192': '🌧️', '1195': '🌧️',
        '1198': '🌨️', '1201': '🌨️', '1204': '🌨️', '1207': '🌨️',
        '1210': '❄️', '1213': '❄️', '1216': '❄️', '1219': '❄️',
        '1222': '❄️', '1225': '❄️', '1237': '🌨️', '1240': '🌦️',
        '1243': '🌧️', '1246': '🌧️', '1249': '🌨️', '1252': '🌨️',
        '1255': '❄️', '1258': '❄️', '1261': '🌨️', '1264': '🌨️',
        '1273': '⛈️', '1276': '⛈️', '1279': '⛈️', '1282': '⛈️'
    },
    weathermap: {
        '01d': '☀️', '01n': '🌙',
        '02d': '⛅', '02n': '☁️',
        '03d': '☁️', '03n': '☁️',
        '04d': '☁️', '04n': '☁️',
        '09d': '🌧️', '09n': '🌧️',
        '10d': '🌦️', '10n': '🌧️',
        '11d': '⛈️', '11n': '⛈️',
        '13d': '❄️', '13n': '❄️',
        '50d': '🌫️', '50n': '🌫️'
    }
};

// 国家温度范围配置
export const COUNTRY_TEMP_RANGES = {
    china: { min: 15, max: 35 },
    usa: { min: 10, max: 30 },
    japan: { min: 12, max: 32 },
    uk: { min: 5, max: 25 },
    france: { min: 8, max: 28 },
    germany: { min: 5, max: 25 },
    australia: { min: 18, max: 38 },
    canada: { min: -5, max: 25 }
};

// 消息类型配置
export const MESSAGE_TYPES = {
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545'
};

// 默认设置
export const DEFAULT_SETTINGS = {
    theme: 'light',
    weatherApiType: 'openweathermap',
    refreshInterval: '10',
    notificationsEnabled: false
};

// 存储键名
export const STORAGE_KEYS = {
    todos: 'todos',
    folders: 'folders',
    weatherCache: 'weatherCache',
    theme: 'theme',
    weatherApiKey: 'weatherApiKey',
    weatherApiType: 'weatherApiType',
    notificationsEnabled: 'notificationsEnabled',
    weatherRefreshInterval: 'weatherRefreshInterval',
    apiTestSuccess: 'apiTestSuccess',
    apiTestTime: 'apiTestTime',
    // 管理员功能相关
    userSessions: 'userSessions',
    userAnalytics: 'userAnalytics',
    adminAuth: 'adminAuth',
    systemLogs: 'systemLogs',
    privacySettings: 'privacySettings'
};

// 管理员配置
export const ADMIN_CONFIG = {
    // 默认管理员密码（实际部署时应该修改）
    DEFAULT_ADMIN_PASSWORD: 'admin123',
    // 会话过期时间（24小时）
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000,
    // 数据收集设置
    DATA_COLLECTION: {
        enabled: true,
        anonymize: true,
        retentionDays: 30
    },
    // IP地址获取API
    IP_API_URL: 'https://api.ipify.org?format=json',
    // 备用IP API
    IP_API_FALLBACK: 'https://httpbin.org/ip'
};

// 用户跟踪配置
export const TRACKING_CONFIG = {
    // 跟踪的事件类型
    EVENTS: {
        APP_START: 'app_start',
        TODO_ADD: 'todo_add',
        TODO_COMPLETE: 'todo_complete',
        TODO_DELETE: 'todo_delete',
        THEME_CHANGE: 'theme_change',
        WEATHER_UPDATE: 'weather_update',
        SETTINGS_CHANGE: 'settings_change',
        FOLDER_CREATE: 'folder_create',
        SEARCH_PERFORM: 'search_perform'
    },
    // 用户指纹组件
    FINGERPRINT_COMPONENTS: [
        'userAgent',
        'language',
        'platform',
        'screenResolution',
        'timezone',
        'colorDepth'
    ]
};

// 数据分析配置
export const ANALYTICS_CONFIG = {
    // 统计维度
    DIMENSIONS: {
        DAILY: 'daily',
        WEEKLY: 'weekly',
        MONTHLY: 'monthly'
    },
    // 图表类型
    CHART_TYPES: {
        LINE: 'line',
        BAR: 'bar',
        PIE: 'pie',
        HEATMAP: 'heatmap'
    }
};
